// Comparison service for the What If investment analysis tool

import { InvestmentCalculator } from './investmentCalculator';
import { BenchmarkDataService } from './benchmarkData';
import { formatCurrency, formatPercentage, calculateYearsBetweenDates } from '../utils';
import {
  InvestmentScenario,
  InvestmentResult,
  ComparisonResult,
  ChartDataPoint,
} from '../types';

export interface ComparisonSummary {
  investment: {
    name: string;
    initialValue: number;
    currentValue: number;
    totalReturn: number;
    cagr: number;
    absoluteReturn: number;
    rank: number;
  };
  benchmarks: Array<{
    name: string;
    type: string;
    initialValue: number;
    currentValue: number;
    totalReturn: number;
    cagr: number;
    absoluteReturn: number;
    rank: number;
    outperformance: number; // How much better/worse than investment
  }>;
  insights: string[];
}

export interface PerformanceMetrics {
  bestPerformer: {
    name: string;
    cagr: number;
    absoluteReturn: number;
  };
  worstPerformer: {
    name: string;
    cagr: number;
    absoluteReturn: number;
  };
  averageCagr: number;
  volatilityRanking: Array<{
    name: string;
    volatility: number;
  }>;
}

export class ComparisonService {
  private investmentCalculator: InvestmentCalculator;
  private benchmarkDataService: BenchmarkDataService;

  constructor(
    investmentCalculator: InvestmentCalculator,
    benchmarkDataService: BenchmarkDataService
  ) {
    this.investmentCalculator = investmentCalculator;
    this.benchmarkDataService = benchmarkDataService;
  }

  /**
   * Generate comprehensive comparison summary
   */
  async generateComparisonSummary(scenario: InvestmentScenario): Promise<ComparisonSummary> {
    try {
      const comparisonResult = await this.investmentCalculator.calculateWithComparisons(scenario);
      
      // Prepare investment data
      const investment = {
        name: `${scenario.stockSymbol} Investment`,
        initialValue: comparisonResult.investment.initialValue,
        currentValue: comparisonResult.investment.currentValue,
        totalReturn: comparisonResult.investment.totalReturn,
        cagr: comparisonResult.investment.cagr,
        absoluteReturn: comparisonResult.investment.absoluteReturn,
        rank: 0, // Will be calculated below
      };

      // Prepare benchmark data
      const benchmarks = Object.entries(comparisonResult.benchmarks).map(([type, data]) => ({
        name: this.getBenchmarkDisplayName(type),
        type,
        initialValue: data.initialValue,
        currentValue: data.currentValue,
        totalReturn: data.currentValue - data.initialValue,
        cagr: data.cagr,
        absoluteReturn: data.absoluteReturn,
        rank: 0, // Will be calculated below
        outperformance: investment.cagr - data.cagr,
      }));

      // Calculate rankings based on CAGR
      const allInvestments = [investment, ...benchmarks];
      allInvestments.sort((a, b) => b.cagr - a.cagr);
      allInvestments.forEach((item, index) => {
        item.rank = index + 1;
      });

      // Generate insights
      const insights = this.generateInsights(investment, benchmarks, scenario);

      return {
        investment,
        benchmarks,
        insights,
      };
    } catch (error) {
      console.error('Error generating comparison summary:', error);
      throw error;
    }
  }

  /**
   * Calculate performance metrics across all investments
   */
  calculatePerformanceMetrics(comparisonSummary: ComparisonSummary): PerformanceMetrics {
    const allInvestments = [comparisonSummary.investment, ...comparisonSummary.benchmarks];
    
    // Find best and worst performers
    const sortedByCagr = [...allInvestments].sort((a, b) => b.cagr - a.cagr);
    const bestPerformer = {
      name: sortedByCagr[0].name,
      cagr: sortedByCagr[0].cagr,
      absoluteReturn: sortedByCagr[0].absoluteReturn,
    };
    const worstPerformer = {
      name: sortedByCagr[sortedByCagr.length - 1].name,
      cagr: sortedByCagr[sortedByCagr.length - 1].cagr,
      absoluteReturn: sortedByCagr[sortedByCagr.length - 1].absoluteReturn,
    };

    // Calculate average CAGR
    const averageCagr = allInvestments.reduce((sum, inv) => sum + inv.cagr, 0) / allInvestments.length;

    // Calculate volatility ranking (simplified - based on absolute return variance from CAGR)
    const volatilityRanking = allInvestments.map(inv => ({
      name: inv.name,
      volatility: Math.abs(inv.absoluteReturn - inv.cagr), // Simplified volatility measure
    })).sort((a, b) => a.volatility - b.volatility);

    return {
      bestPerformer,
      worstPerformer,
      averageCagr: Number(averageCagr.toFixed(2)),
      volatilityRanking,
    };
  }

  /**
   * Generate chart data for comparison visualization
   */
  async generateComparisonChartData(scenario: InvestmentScenario): Promise<{
    timeSeriesData: Array<{
      date: string;
      investment: number;
      gold: number;
      fd: number;
      nifty: number;
    }>;
    barChartData: Array<{
      name: string;
      cagr: number;
      absoluteReturn: number;
      currentValue: number;
    }>;
  }> {
    try {
      const comparisonResult = await this.investmentCalculator.calculateWithComparisons(scenario);
      
      // For time series, we'll need to get historical data for all benchmarks
      // This is a simplified version - in production, you'd want actual time series data
      const timeSeriesData = await this.generateTimeSeriesData(scenario, comparisonResult);
      
      // Bar chart data for final comparison
      const barChartData = [
        {
          name: scenario.stockSymbol,
          cagr: comparisonResult.investment.cagr,
          absoluteReturn: comparisonResult.investment.absoluteReturn,
          currentValue: comparisonResult.investment.currentValue,
        },
        ...Object.entries(comparisonResult.benchmarks).map(([type, data]) => ({
          name: this.getBenchmarkDisplayName(type),
          cagr: data.cagr,
          absoluteReturn: data.absoluteReturn,
          currentValue: data.currentValue,
        })),
      ];

      return {
        timeSeriesData,
        barChartData,
      };
    } catch (error) {
      console.error('Error generating chart data:', error);
      throw error;
    }
  }

  /**
   * Compare multiple stocks against benchmarks
   */
  async compareMultipleStocks(
    scenarios: InvestmentScenario[]
  ): Promise<Array<{
    scenario: InvestmentScenario;
    summary: ComparisonSummary;
  }>> {
    const results: Array<{
      scenario: InvestmentScenario;
      summary: ComparisonSummary;
    }> = [];

    for (const scenario of scenarios) {
      try {
        const summary = await this.generateComparisonSummary(scenario);
        results.push({ scenario, summary });
      } catch (error) {
        console.error(`Error comparing scenario ${scenario.id}:`, error);
        // Continue with other scenarios
      }
    }

    return results;
  }

  /**
   * Generate insights based on comparison results
   */
  private generateInsights(
    investment: any,
    benchmarks: any[],
    scenario: InvestmentScenario
  ): string[] {
    const insights: string[] = [];
    const years = calculateYearsBetweenDates(scenario.startDate, scenario.endDate);

    // Performance insights
    if (investment.rank === 1) {
      insights.push(`🎉 Your ${scenario.stockSymbol} investment outperformed all benchmarks with a CAGR of ${formatPercentage(investment.cagr)}.`);
    } else {
      const betterBenchmarks = benchmarks.filter(b => b.rank < investment.rank);
      if (betterBenchmarks.length > 0) {
        const bestBenchmark = betterBenchmarks[0];
        insights.push(`📊 Your investment ranked #${investment.rank}. ${bestBenchmark.name} performed better with ${formatPercentage(bestBenchmark.cagr)} CAGR.`);
      }
    }

    // Risk-adjusted insights
    const goldBenchmark = benchmarks.find(b => b.type === 'GOLD');
    const fdBenchmark = benchmarks.find(b => b.type === 'FD');
    const niftyBenchmark = benchmarks.find(b => b.type === 'NIFTY');

    if (goldBenchmark && investment.cagr > goldBenchmark.cagr) {
      insights.push(`🥇 Your investment beat gold by ${formatPercentage(investment.cagr - goldBenchmark.cagr)} annually.`);
    }

    if (fdBenchmark && investment.cagr > fdBenchmark.cagr) {
      insights.push(`🏦 Your investment outperformed Fixed Deposits by ${formatPercentage(investment.cagr - fdBenchmark.cagr)} annually.`);
    }

    if (niftyBenchmark) {
      if (investment.cagr > niftyBenchmark.cagr) {
        insights.push(`📈 Your stock selection beat the market (Nifty 50) by ${formatPercentage(investment.cagr - niftyBenchmark.cagr)} annually.`);
      } else {
        insights.push(`📉 The market (Nifty 50) outperformed your stock by ${formatPercentage(niftyBenchmark.cagr - investment.cagr)} annually.`);
      }
    }

    // Time-based insights
    if (years >= 5) {
      insights.push(`⏰ Over ${Math.round(years)} years, your ${formatCurrency(scenario.investmentAmount)} investment grew to ${formatCurrency(investment.currentValue)}.`);
    } else if (years >= 1) {
      insights.push(`📅 In ${Math.round(years * 12)} months, your investment generated ${formatCurrency(investment.totalReturn)} in returns.`);
    }

    // Value insights
    if (investment.absoluteReturn > 100) {
      insights.push(`💰 Your investment more than doubled your money with ${formatPercentage(investment.absoluteReturn)} total returns.`);
    } else if (investment.absoluteReturn > 50) {
      insights.push(`💵 Your investment generated strong returns of ${formatPercentage(investment.absoluteReturn)}.`);
    } else if (investment.absoluteReturn < 0) {
      insights.push(`⚠️ Your investment resulted in a loss of ${formatPercentage(Math.abs(investment.absoluteReturn))}.`);
    }

    return insights;
  }

  /**
   * Get display name for benchmark type
   */
  private getBenchmarkDisplayName(type: string): string {
    const names: { [key: string]: string } = {
      'GOLD': 'Gold',
      'FD': 'Fixed Deposit',
      'NIFTY': 'Nifty 50',
    };
    return names[type] || type;
  }

  /**
   * Generate time series data for visualization
   */
  private async generateTimeSeriesData(
    scenario: InvestmentScenario,
    comparisonResult: ComparisonResult
  ): Promise<Array<{
    date: string;
    investment: number;
    gold: number;
    fd: number;
    nifty: number;
  }>> {
    // This is a simplified implementation
    // In production, you'd fetch actual historical data for all assets
    const data: Array<{
      date: string;
      investment: number;
      gold: number;
      fd: number;
      nifty: number;
    }> = [];

    const startDate = new Date(scenario.startDate);
    const endDate = new Date(scenario.endDate);
    const currentDate = new Date(startDate);

    // Calculate daily growth rates
    const years = calculateYearsBetweenDates(startDate, endDate);
    const days = years * 365;

    const investmentDailyGrowth = Math.pow(comparisonResult.investment.currentValue / comparisonResult.investment.initialValue, 1 / days);
    const goldDailyGrowth = Math.pow(comparisonResult.benchmarks.GOLD.currentValue / comparisonResult.benchmarks.GOLD.initialValue, 1 / days);
    const fdDailyGrowth = Math.pow(comparisonResult.benchmarks.FD.currentValue / comparisonResult.benchmarks.FD.initialValue, 1 / days);
    const niftyDailyGrowth = Math.pow(comparisonResult.benchmarks.NIFTY.currentValue / comparisonResult.benchmarks.NIFTY.initialValue, 1 / days);

    let dayCount = 0;
    while (currentDate <= endDate) {
      const investmentValue = scenario.investmentAmount * Math.pow(investmentDailyGrowth, dayCount);
      const goldValue = scenario.investmentAmount * Math.pow(goldDailyGrowth, dayCount);
      const fdValue = scenario.investmentAmount * Math.pow(fdDailyGrowth, dayCount);
      const niftyValue = scenario.investmentAmount * Math.pow(niftyDailyGrowth, dayCount);

      data.push({
        date: currentDate.toISOString().split('T')[0],
        investment: Math.round(investmentValue),
        gold: Math.round(goldValue),
        fd: Math.round(fdValue),
        nifty: Math.round(niftyValue),
      });

      currentDate.setDate(currentDate.getDate() + 7); // Weekly data points
      dayCount += 7;
    }

    return data;
  }
}
