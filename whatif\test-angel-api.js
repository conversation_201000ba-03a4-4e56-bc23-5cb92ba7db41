// Test script to verify Angel One API connection
const axios = require('axios');
const { authenticator } = require('otplib');

// Your credentials
const API_KEY = 'TU9sOEpR';
const CLIENT_ID = 'M834963';
const PASSWORD = '3318';
const TOTP_SECRET = 'CRAFUYSVQVWTWPHVWZ55KV5VJI';

const BASE_URL = 'https://apiconnect.angelone.in';

async function testAngelOneAPI() {
  try {
    console.log('🔄 Testing Angel One API connection...');
    
    // Generate TOTP
    const totp = authenticator.generate(TOTP_SECRET);
    console.log('📱 Generated TOTP:', totp);

    // Create axios instance
    const apiClient = axios.create({
      baseURL: BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-UserType': 'USER',
        'X-SourceID': 'WEB',
        'X-ClientLocalIP': '127.0.0.1',
        'X-ClientPublicIP': '127.0.0.1',
        'X-MACAddress': '00:00:00:00:00:00',
        'X-PrivateKey': API_KEY,
      },
    });

    // Login request
    const loginData = {
      clientcode: CLIENT_ID,
      password: PASSWORD,
      totp: totp,
    };

    console.log('🔐 Attempting login with client ID:', CLIENT_ID);
    
    const response = await apiClient.post('/rest/auth/angelbroking/user/v1/loginByPassword', loginData);
    
    console.log('✅ Login Response Status:', response.status);
    console.log('📊 Login Response Data:', JSON.stringify(response.data, null, 2));

    if (response.data.status && response.data.data) {
      const { jwtToken, refreshToken, feedToken } = response.data.data;
      console.log('🎉 Login successful!');
      console.log('🔑 JWT Token received:', jwtToken ? 'Yes' : 'No');
      console.log('🔄 Refresh Token received:', refreshToken ? 'Yes' : 'No');
      console.log('📡 Feed Token received:', feedToken ? 'Yes' : 'No');

      // Test historical data endpoint
      console.log('\n🔄 Testing historical data endpoint...');
      
      const historicalRequest = {
        exchange: 'NSE',
        symboltoken: '3045', // SBIN token
        interval: 'ONE_DAY',
        fromdate: '2024-01-01 09:15',
        todate: '2024-01-31 15:30',
      };

      const historicalResponse = await apiClient.post(
        '/rest/secure/angelbroking/historical/v1/getCandleData',
        historicalRequest,
        {
          headers: {
            Authorization: `Bearer ${jwtToken}`,
          },
        }
      );

      console.log('📈 Historical Data Response Status:', historicalResponse.status);
      console.log('📊 Historical Data Response:', JSON.stringify(historicalResponse.data, null, 2));

      // Test LTP endpoint
      console.log('\n🔄 Testing LTP (Last Traded Price) endpoint...');
      
      const ltpRequest = {
        exchange: 'NSE',
        tradingsymbol: 'SBIN-EQ',
        symboltoken: '3045',
      };

      const ltpResponse = await apiClient.post(
        '/rest/secure/angelbroking/order/v1/getLtpData',
        ltpRequest,
        {
          headers: {
            Authorization: `Bearer ${jwtToken}`,
          },
        }
      );

      console.log('💰 LTP Response Status:', ltpResponse.status);
      console.log('📊 LTP Response:', JSON.stringify(ltpResponse.data, null, 2));

    } else {
      console.log('❌ Login failed:', response.data.message);
    }

  } catch (error) {
    console.error('❌ API Test Error:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
      console.error('Headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testAngelOneAPI();
