[{"name": "generate-buildid", "duration": 406, "timestamp": 136058508285, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752045372621, "traceId": "659758555722ad1d"}, {"name": "load-custom-routes", "duration": 687, "timestamp": 136058508907, "id": 5, "parentId": 1, "tags": {}, "startTime": 1752045372622, "traceId": "659758555722ad1d"}, {"name": "create-dist-dir", "duration": 849, "timestamp": 136058674607, "id": 6, "parentId": 1, "tags": {}, "startTime": 1752045372787, "traceId": "659758555722ad1d"}, {"name": "create-pages-mapping", "duration": 693, "timestamp": 136058726208, "id": 7, "parentId": 1, "tags": {}, "startTime": 1752045372839, "traceId": "659758555722ad1d"}, {"name": "collect-app-paths", "duration": 6483, "timestamp": 136058729063, "id": 8, "parentId": 1, "tags": {}, "startTime": 1752045372842, "traceId": "659758555722ad1d"}, {"name": "create-app-mapping", "duration": 7756, "timestamp": 136058735626, "id": 9, "parentId": 1, "tags": {}, "startTime": 1752045372848, "traceId": "659758555722ad1d"}, {"name": "public-dir-conflict-check", "duration": 1869, "timestamp": 136058748378, "id": 10, "parentId": 1, "tags": {}, "startTime": 1752045372861, "traceId": "659758555722ad1d"}, {"name": "generate-routes-manifest", "duration": 8311, "timestamp": 136058751301, "id": 11, "parentId": 1, "tags": {}, "startTime": 1752045372864, "traceId": "659758555722ad1d"}, {"name": "create-entrypoints", "duration": 52084, "timestamp": 136061488779, "id": 15, "parentId": 13, "tags": {}, "startTime": 1752045375605, "traceId": "659758555722ad1d"}, {"name": "generate-webpack-config", "duration": 1044218, "timestamp": 136061541244, "id": 16, "parentId": 14, "tags": {}, "startTime": 1752045375657, "traceId": "659758555722ad1d"}, {"name": "next-trace-entrypoint-plugin", "duration": 8033, "timestamp": 136062871712, "id": 18, "parentId": 17, "tags": {}, "startTime": 1752045376988, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 632595, "timestamp": 136062896895, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1752045377013, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 806206, "timestamp": 136062897076, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1752045377013, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 806362, "timestamp": 136062896971, "id": 23, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1752045377013, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 846330, "timestamp": 136062896496, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CMeet%5CDocuments%5Caugment-projects%5Cwhatif%5Cwhatif%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752045377012, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 869830, "timestamp": 136062894174, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CMeet%5CDocuments%5Caugment-projects%5Cwhatif%5Cwhatif%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752045377010, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 877116, "timestamp": 136062897011, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMeet%5CDocuments%5Caugment-projects%5Cwhatif%5Cwhatif%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752045377013, "traceId": "659758555722ad1d"}, {"name": "make", "duration": 1104001, "timestamp": 136062892873, "id": 19, "parentId": 17, "tags": {}, "startTime": 1752045377009, "traceId": "659758555722ad1d"}, {"name": "get-entries", "duration": 3195, "timestamp": 136063999550, "id": 37, "parentId": 36, "tags": {}, "startTime": 1752045378115, "traceId": "659758555722ad1d"}, {"name": "node-file-trace-plugin", "duration": 239183, "timestamp": 136064013077, "id": 38, "parentId": 36, "tags": {"traceEntryCount": "8"}, "startTime": 1752045378129, "traceId": "659758555722ad1d"}, {"name": "collect-traced-files", "duration": 904, "timestamp": 136064252278, "id": 39, "parentId": 36, "tags": {}, "startTime": 1752045378368, "traceId": "659758555722ad1d"}, {"name": "finish-modules", "duration": 254089, "timestamp": 136063999103, "id": 36, "parentId": 18, "tags": {}, "startTime": 1752045378115, "traceId": "659758555722ad1d"}, {"name": "chunk-graph", "duration": 35584, "timestamp": 136064313424, "id": 41, "parentId": 40, "tags": {}, "startTime": 1752045378429, "traceId": "659758555722ad1d"}, {"name": "optimize-modules", "duration": 132, "timestamp": 136064349473, "id": 43, "parentId": 40, "tags": {}, "startTime": 1752045378465, "traceId": "659758555722ad1d"}, {"name": "optimize-chunks", "duration": 21473, "timestamp": 136064350004, "id": 44, "parentId": 40, "tags": {}, "startTime": 1752045378466, "traceId": "659758555722ad1d"}, {"name": "optimize-tree", "duration": 329, "timestamp": 136064371859, "id": 45, "parentId": 40, "tags": {}, "startTime": 1752045378488, "traceId": "659758555722ad1d"}, {"name": "optimize-chunk-modules", "duration": 11933, "timestamp": 136064372389, "id": 46, "parentId": 40, "tags": {}, "startTime": 1752045378488, "traceId": "659758555722ad1d"}, {"name": "optimize", "duration": 35274, "timestamp": 136064349251, "id": 42, "parentId": 40, "tags": {}, "startTime": 1752045378465, "traceId": "659758555722ad1d"}, {"name": "module-hash", "duration": 24016, "timestamp": 136064418902, "id": 47, "parentId": 40, "tags": {}, "startTime": 1752045378535, "traceId": "659758555722ad1d"}, {"name": "code-generation", "duration": 15553, "timestamp": 136064443036, "id": 48, "parentId": 40, "tags": {}, "startTime": 1752045378559, "traceId": "659758555722ad1d"}, {"name": "hash", "duration": 18069, "timestamp": 136064469955, "id": 49, "parentId": 40, "tags": {}, "startTime": 1752045378586, "traceId": "659758555722ad1d"}, {"name": "code-generation-jobs", "duration": 552, "timestamp": 136064488017, "id": 50, "parentId": 40, "tags": {}, "startTime": 1752045378604, "traceId": "659758555722ad1d"}, {"name": "module-assets", "duration": 375, "timestamp": 136064488477, "id": 51, "parentId": 40, "tags": {}, "startTime": 1752045378604, "traceId": "659758555722ad1d"}, {"name": "create-chunk-assets", "duration": 3853, "timestamp": 136064488876, "id": 52, "parentId": 40, "tags": {}, "startTime": 1752045378605, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 1182, "timestamp": 136064514870, "id": 54, "parentId": 53, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1752045378631, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 428, "timestamp": 136064515641, "id": 55, "parentId": 53, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1752045378631, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 389, "timestamp": 136064515688, "id": 56, "parentId": 53, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1752045378631, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 370, "timestamp": 136064515709, "id": 57, "parentId": 53, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1752045378632, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 356, "timestamp": 136064515725, "id": 58, "parentId": 53, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1752045378632, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 342, "timestamp": 136064515742, "id": 59, "parentId": 53, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1752045378632, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 329, "timestamp": 136064515758, "id": 60, "parentId": 53, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1752045378632, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 310, "timestamp": 136064515779, "id": 61, "parentId": 53, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1752045378632, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 297, "timestamp": 136064515793, "id": 62, "parentId": 53, "tags": {"name": "145.js", "cache": "HIT"}, "startTime": 1752045378632, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 157, "timestamp": 136064515935, "id": 63, "parentId": 53, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1752045378632, "traceId": "659758555722ad1d"}, {"name": "minify-webpack-plugin-optimize", "duration": 14654, "timestamp": 136064501455, "id": 53, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1752045378617, "traceId": "659758555722ad1d"}, {"name": "css-minimizer-plugin", "duration": 269, "timestamp": 136064516484, "id": 64, "parentId": 17, "tags": {}, "startTime": 1752045378632, "traceId": "659758555722ad1d"}, {"name": "create-trace-assets", "duration": 3083, "timestamp": 136064517201, "id": 65, "parentId": 18, "tags": {}, "startTime": 1752045378633, "traceId": "659758555722ad1d"}, {"name": "seal", "duration": 251099, "timestamp": 136064285487, "id": 40, "parentId": 17, "tags": {}, "startTime": 1752045378401, "traceId": "659758555722ad1d"}, {"name": "webpack-compilation", "duration": 1686361, "timestamp": 136062867665, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752045376983, "traceId": "659758555722ad1d"}, {"name": "emit", "duration": 35131, "timestamp": 136064555877, "id": 66, "parentId": 14, "tags": {}, "startTime": 1752045378672, "traceId": "659758555722ad1d"}, {"name": "webpack-close", "duration": 342493, "timestamp": 136064600067, "id": 67, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752045378716, "traceId": "659758555722ad1d"}, {"name": "webpack-generate-error-stats", "duration": 9833, "timestamp": 136064942699, "id": 68, "parentId": 67, "tags": {}, "startTime": 1752045379059, "traceId": "659758555722ad1d"}, {"name": "run-webpack-compiler", "duration": 3464387, "timestamp": 136061488756, "id": 14, "parentId": 13, "tags": {}, "startTime": 1752045375605, "traceId": "659758555722ad1d"}, {"name": "format-webpack-messages", "duration": 187, "timestamp": 136064953155, "id": 69, "parentId": 13, "tags": {}, "startTime": 1752045379069, "traceId": "659758555722ad1d"}, {"name": "worker-main-server", "duration": 3466106, "timestamp": 136061487535, "id": 13, "parentId": 1, "tags": {}, "startTime": 1752045375603, "traceId": "659758555722ad1d"}, {"name": "create-entrypoints", "duration": 47593, "timestamp": 136067717984, "id": 72, "parentId": 70, "tags": {}, "startTime": 1752045381833, "traceId": "659758555722ad1d"}, {"name": "generate-webpack-config", "duration": 1212210, "timestamp": 136067766270, "id": 73, "parentId": 71, "tags": {}, "startTime": 1752045381881, "traceId": "659758555722ad1d"}, {"name": "make", "duration": 4018, "timestamp": 136069333901, "id": 75, "parentId": 74, "tags": {}, "startTime": 1752045383449, "traceId": "659758555722ad1d"}, {"name": "chunk-graph", "duration": 3110, "timestamp": 136069366573, "id": 77, "parentId": 76, "tags": {}, "startTime": 1752045383482, "traceId": "659758555722ad1d"}, {"name": "optimize-modules", "duration": 283, "timestamp": 136069370330, "id": 79, "parentId": 76, "tags": {}, "startTime": 1752045383485, "traceId": "659758555722ad1d"}, {"name": "optimize-chunks", "duration": 6538, "timestamp": 136069372334, "id": 80, "parentId": 76, "tags": {}, "startTime": 1752045383487, "traceId": "659758555722ad1d"}, {"name": "optimize-tree", "duration": 1776, "timestamp": 136069379725, "id": 81, "parentId": 76, "tags": {}, "startTime": 1752045383495, "traceId": "659758555722ad1d"}, {"name": "optimize-chunk-modules", "duration": 2212, "timestamp": 136069382776, "id": 82, "parentId": 76, "tags": {}, "startTime": 1752045383498, "traceId": "659758555722ad1d"}, {"name": "optimize", "duration": 15349, "timestamp": 136069370054, "id": 78, "parentId": 76, "tags": {}, "startTime": 1752045383485, "traceId": "659758555722ad1d"}, {"name": "module-hash", "duration": 231, "timestamp": 136069389751, "id": 83, "parentId": 76, "tags": {}, "startTime": 1752045383505, "traceId": "659758555722ad1d"}, {"name": "code-generation", "duration": 573, "timestamp": 136069390094, "id": 84, "parentId": 76, "tags": {}, "startTime": 1752045383505, "traceId": "659758555722ad1d"}, {"name": "hash", "duration": 1665, "timestamp": 136069391596, "id": 85, "parentId": 76, "tags": {}, "startTime": 1752045383507, "traceId": "659758555722ad1d"}, {"name": "code-generation-jobs", "duration": 958, "timestamp": 136069393199, "id": 86, "parentId": 76, "tags": {}, "startTime": 1752045383508, "traceId": "659758555722ad1d"}, {"name": "module-assets", "duration": 564, "timestamp": 136069393920, "id": 87, "parentId": 76, "tags": {}, "startTime": 1752045383509, "traceId": "659758555722ad1d"}, {"name": "create-chunk-assets", "duration": 1115, "timestamp": 136069394613, "id": 88, "parentId": 76, "tags": {}, "startTime": 1752045383510, "traceId": "659758555722ad1d"}, {"name": "minify-js", "duration": 455, "timestamp": 136069434528, "id": 90, "parentId": 89, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1752045383550, "traceId": "659758555722ad1d"}, {"name": "minify-webpack-plugin-optimize", "duration": 10043, "timestamp": 136069425012, "id": 89, "parentId": 74, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1752045383540, "traceId": "659758555722ad1d"}, {"name": "css-minimizer-plugin", "duration": 863, "timestamp": 136069435396, "id": 91, "parentId": 74, "tags": {}, "startTime": 1752045383551, "traceId": "659758555722ad1d"}, {"name": "seal", "duration": 95436, "timestamp": 136069358663, "id": 76, "parentId": 74, "tags": {}, "startTime": 1752045383474, "traceId": "659758555722ad1d"}, {"name": "webpack-compilation", "duration": 157183, "timestamp": 136069297838, "id": 74, "parentId": 71, "tags": {"name": "edge-server"}, "startTime": 1752045383413, "traceId": "659758555722ad1d"}, {"name": "emit", "duration": 13300, "timestamp": 136069455992, "id": 92, "parentId": 71, "tags": {}, "startTime": 1752045383571, "traceId": "659758555722ad1d"}, {"name": "webpack-close", "duration": 2956, "timestamp": 136069477595, "id": 93, "parentId": 71, "tags": {"name": "edge-server"}, "startTime": 1752045383593, "traceId": "659758555722ad1d"}, {"name": "webpack-generate-error-stats", "duration": 14172, "timestamp": 136069480734, "id": 94, "parentId": 93, "tags": {}, "startTime": 1752045383596, "traceId": "659758555722ad1d"}, {"name": "run-webpack-compiler", "duration": 1777278, "timestamp": 136067717967, "id": 71, "parentId": 70, "tags": {}, "startTime": 1752045381833, "traceId": "659758555722ad1d"}, {"name": "format-webpack-messages", "duration": 225, "timestamp": 136069495258, "id": 95, "parentId": 70, "tags": {}, "startTime": 1752045383610, "traceId": "659758555722ad1d"}, {"name": "worker-main-edge-server", "duration": 1779142, "timestamp": 136067717149, "id": 70, "parentId": 1, "tags": {}, "startTime": 1752045381832, "traceId": "659758555722ad1d"}, {"name": "create-entrypoints", "duration": 72947, "timestamp": 136071841313, "id": 98, "parentId": 96, "tags": {}, "startTime": 1752045385958, "traceId": "659758555722ad1d"}, {"name": "generate-webpack-config", "duration": 1299708, "timestamp": 136071914624, "id": 99, "parentId": 97, "tags": {}, "startTime": 1752045386031, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 718714, "timestamp": 136073631195, "id": 105, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1752045387748, "traceId": "659758555722ad1d"}, {"name": "postcss-process", "duration": 259262, "timestamp": 136075158954, "id": 114, "parentId": 113, "tags": {}, "startTime": 1752045389276, "traceId": "659758555722ad1d"}, {"name": "postcss-loader", "duration": 933078, "timestamp": 136074485512, "id": 113, "parentId": 112, "tags": {}, "startTime": 1752045388602, "traceId": "659758555722ad1d"}, {"name": "css-loader", "duration": 84333, "timestamp": 136075419100, "id": 115, "parentId": 112, "tags": {"astUsed": "true"}, "startTime": 1752045389536, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 1920732, "timestamp": 136073631609, "id": 110, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752045387748, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 1921256, "timestamp": 136073631135, "id": 104, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752045387748, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 1921374, "timestamp": 136073631041, "id": 103, "parentId": 101, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1752045387748, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 1921029, "timestamp": 136073631437, "id": 106, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1752045387748, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 1920970, "timestamp": 136073631529, "id": 107, "parentId": 101, "tags": {"request": "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1752045387748, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 1920980, "timestamp": 136073631563, "id": 108, "parentId": 101, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1752045387748, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 1923519, "timestamp": 136073629974, "id": 102, "parentId": 101, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1752045387747, "traceId": "659758555722ad1d"}, {"name": "build-module-css", "duration": 1082989, "timestamp": 136074471847, "id": 112, "parentId": 111, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\globals.css", "layer": null}, "startTime": 1752045388589, "traceId": "659758555722ad1d"}, {"name": "build-module-css", "duration": 1151432, "timestamp": 136074427970, "id": 111, "parentId": 100, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\globals.css", "layer": "app-pages-browser"}, "startTime": 1752045388545, "traceId": "659758555722ad1d"}, {"name": "build-module", "duration": 242, "timestamp": 136075579707, "id": 116, "parentId": 111, "tags": {}, "startTime": 1752045389696, "traceId": "659758555722ad1d"}, {"name": "add-entry", "duration": 1948422, "timestamp": 136073631589, "id": 109, "parentId": 101, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752045387748, "traceId": "659758555722ad1d"}, {"name": "make", "duration": 1951686, "timestamp": 136073628839, "id": 101, "parentId": 100, "tags": {}, "startTime": 1752045387746, "traceId": "659758555722ad1d"}, {"name": "chunk-graph", "duration": 13967, "timestamp": 136075614973, "id": 118, "parentId": 117, "tags": {}, "startTime": 1752045389732, "traceId": "659758555722ad1d"}, {"name": "optimize-modules", "duration": 71, "timestamp": 136075629181, "id": 120, "parentId": 117, "tags": {}, "startTime": 1752045389746, "traceId": "659758555722ad1d"}]