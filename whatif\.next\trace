[{"name": "generate-buildid", "duration": 408, "timestamp": 135315859530, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752044629973, "traceId": "b6649d4189b87d9e"}, {"name": "load-custom-routes", "duration": 656, "timestamp": 135315860131, "id": 5, "parentId": 1, "tags": {}, "startTime": 1752044629974, "traceId": "b6649d4189b87d9e"}, {"name": "create-dist-dir", "duration": 851, "timestamp": 135316034250, "id": 6, "parentId": 1, "tags": {}, "startTime": 1752044630148, "traceId": "b6649d4189b87d9e"}, {"name": "create-pages-mapping", "duration": 702, "timestamp": 135316084347, "id": 7, "parentId": 1, "tags": {}, "startTime": 1752044630198, "traceId": "b6649d4189b87d9e"}, {"name": "collect-app-paths", "duration": 4700, "timestamp": 135316085184, "id": 8, "parentId": 1, "tags": {}, "startTime": 1752044630199, "traceId": "b6649d4189b87d9e"}, {"name": "create-app-mapping", "duration": 14122, "timestamp": 135316089961, "id": 9, "parentId": 1, "tags": {}, "startTime": 1752044630203, "traceId": "b6649d4189b87d9e"}, {"name": "public-dir-conflict-check", "duration": 2011, "timestamp": 135316105461, "id": 10, "parentId": 1, "tags": {}, "startTime": 1752044630219, "traceId": "b6649d4189b87d9e"}, {"name": "generate-routes-manifest", "duration": 8695, "timestamp": 135316108321, "id": 11, "parentId": 1, "tags": {}, "startTime": 1752044630222, "traceId": "b6649d4189b87d9e"}, {"name": "create-entrypoints", "duration": 53631, "timestamp": 135318699570, "id": 15, "parentId": 13, "tags": {}, "startTime": 1752044632814, "traceId": "b6649d4189b87d9e"}, {"name": "generate-webpack-config", "duration": 1151055, "timestamp": 135318753543, "id": 16, "parentId": 14, "tags": {}, "startTime": 1752044632868, "traceId": "b6649d4189b87d9e"}, {"name": "next-trace-entrypoint-plugin", "duration": 14981, "timestamp": 135321070294, "id": 18, "parentId": 17, "tags": {}, "startTime": 1752044635185, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 2731797, "timestamp": 135321171535, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1752044635286, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 2731808, "timestamp": 135321171646, "id": 23, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1752044635286, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 2753059, "timestamp": 135321171781, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1752044635286, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 2930337, "timestamp": 135321168707, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CMeet%5CDocuments%5Caugment-projects%5Cwhatif%5Cwhatif%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752044635283, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 2935689, "timestamp": 135321170978, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CMeet%5CDocuments%5Caugment-projects%5Cwhatif%5Cwhatif%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752044635286, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 2938775, "timestamp": 135321171725, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMeet%5CDocuments%5Caugment-projects%5Cwhatif%5Cwhatif%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1752044635286, "traceId": "b6649d4189b87d9e"}, {"name": "make", "duration": 3299399, "timestamp": 135321167353, "id": 19, "parentId": 17, "tags": {}, "startTime": 1752044635282, "traceId": "b6649d4189b87d9e"}, {"name": "get-entries", "duration": 3966, "timestamp": 135324470625, "id": 37, "parentId": 36, "tags": {}, "startTime": 1752044638585, "traceId": "b6649d4189b87d9e"}, {"name": "node-file-trace-plugin", "duration": 902604, "timestamp": 135324488545, "id": 38, "parentId": 36, "tags": {"traceEntryCount": "8"}, "startTime": 1752044638603, "traceId": "b6649d4189b87d9e"}, {"name": "collect-traced-files", "duration": 1764, "timestamp": 135325391173, "id": 39, "parentId": 36, "tags": {}, "startTime": 1752044639506, "traceId": "b6649d4189b87d9e"}, {"name": "finish-modules", "duration": 922811, "timestamp": 135324470147, "id": 36, "parentId": 18, "tags": {}, "startTime": 1752044638585, "traceId": "b6649d4189b87d9e"}, {"name": "chunk-graph", "duration": 41449, "timestamp": 135325601657, "id": 41, "parentId": 40, "tags": {}, "startTime": 1752044639716, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-modules", "duration": 96, "timestamp": 135325643484, "id": 43, "parentId": 40, "tags": {}, "startTime": 1752044639758, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-chunks", "duration": 59125, "timestamp": 135325643854, "id": 44, "parentId": 40, "tags": {}, "startTime": 1752044639758, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-tree", "duration": 669, "timestamp": 135325703923, "id": 45, "parentId": 40, "tags": {}, "startTime": 1752044639818, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-chunk-modules", "duration": 21319, "timestamp": 135325705025, "id": 46, "parentId": 40, "tags": {}, "startTime": 1752044639820, "traceId": "b6649d4189b87d9e"}, {"name": "optimize", "duration": 83242, "timestamp": 135325643316, "id": 42, "parentId": 40, "tags": {}, "startTime": 1752044639758, "traceId": "b6649d4189b87d9e"}, {"name": "module-hash", "duration": 38396, "timestamp": 135325852298, "id": 47, "parentId": 40, "tags": {}, "startTime": 1752044639967, "traceId": "b6649d4189b87d9e"}, {"name": "code-generation", "duration": 44992, "timestamp": 135325890805, "id": 48, "parentId": 40, "tags": {}, "startTime": 1752044640005, "traceId": "b6649d4189b87d9e"}, {"name": "hash", "duration": 49169, "timestamp": 135325953446, "id": 49, "parentId": 40, "tags": {}, "startTime": 1752044640068, "traceId": "b6649d4189b87d9e"}, {"name": "code-generation-jobs", "duration": 851, "timestamp": 135326002603, "id": 50, "parentId": 40, "tags": {}, "startTime": 1752044640117, "traceId": "b6649d4189b87d9e"}, {"name": "module-assets", "duration": 554, "timestamp": 135326003342, "id": 51, "parentId": 40, "tags": {}, "startTime": 1752044640118, "traceId": "b6649d4189b87d9e"}, {"name": "create-chunk-assets", "duration": 30420, "timestamp": 135326003951, "id": 52, "parentId": 40, "tags": {}, "startTime": 1752044640118, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 38385, "timestamp": 135326101340, "id": 54, "parentId": 53, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1752044640216, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 18763, "timestamp": 135326120984, "id": 56, "parentId": 53, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1752044640236, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 18657, "timestamp": 135326121108, "id": 57, "parentId": 53, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1752044640236, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 18637, "timestamp": 135326121133, "id": 58, "parentId": 53, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1752044640236, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 18619, "timestamp": 135326121155, "id": 59, "parentId": 53, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1752044640236, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 18609, "timestamp": 135326121173, "id": 60, "parentId": 53, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1752044640236, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 18598, "timestamp": 135326121190, "id": 61, "parentId": 53, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1752044640236, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 157, "timestamp": 135326139635, "id": 63, "parentId": 53, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1752044640254, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 355279, "timestamp": 135326102488, "id": 55, "parentId": 53, "tags": {"name": "../app/favicon.ico/route.js", "cache": "MISS"}, "startTime": 1752044640217, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 433159, "timestamp": 135326121205, "id": 62, "parentId": 53, "tags": {"name": "145.js", "cache": "MISS"}, "startTime": 1752044640236, "traceId": "b6649d4189b87d9e"}, {"name": "minify-webpack-plugin-optimize", "duration": 499956, "timestamp": 135326054428, "id": 53, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1752044640169, "traceId": "b6649d4189b87d9e"}, {"name": "css-minimizer-plugin", "duration": 372, "timestamp": 135326554841, "id": 64, "parentId": 17, "tags": {}, "startTime": 1752044640669, "traceId": "b6649d4189b87d9e"}, {"name": "create-trace-assets", "duration": 3518, "timestamp": 135326555668, "id": 65, "parentId": 18, "tags": {}, "startTime": 1752044640670, "traceId": "b6649d4189b87d9e"}, {"name": "seal", "duration": 1089743, "timestamp": 135325492878, "id": 40, "parentId": 17, "tags": {}, "startTime": 1752044639607, "traceId": "b6649d4189b87d9e"}, {"name": "webpack-compilation", "duration": 5550649, "timestamp": 135321050084, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752044635165, "traceId": "b6649d4189b87d9e"}, {"name": "emit", "duration": 26820, "timestamp": 135326604361, "id": 66, "parentId": 14, "tags": {}, "startTime": 1752044640719, "traceId": "b6649d4189b87d9e"}, {"name": "webpack-close", "duration": 851930, "timestamp": 135326636329, "id": 67, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752044640751, "traceId": "b6649d4189b87d9e"}, {"name": "webpack-generate-error-stats", "duration": 22394, "timestamp": 135327488485, "id": 68, "parentId": 67, "tags": {}, "startTime": 1752044641603, "traceId": "b6649d4189b87d9e"}, {"name": "run-webpack-compiler", "duration": 8814379, "timestamp": 135318699551, "id": 14, "parentId": 13, "tags": {}, "startTime": 1752044632814, "traceId": "b6649d4189b87d9e"}, {"name": "format-webpack-messages", "duration": 468, "timestamp": 135327513957, "id": 69, "parentId": 13, "tags": {}, "startTime": 1752044641629, "traceId": "b6649d4189b87d9e"}, {"name": "worker-main-server", "duration": 8816292, "timestamp": 135318698664, "id": 13, "parentId": 1, "tags": {}, "startTime": 1752044632813, "traceId": "b6649d4189b87d9e"}, {"name": "create-entrypoints", "duration": 74050, "timestamp": 135331314992, "id": 73, "parentId": 71, "tags": {}, "startTime": 1752044645431, "traceId": "b6649d4189b87d9e"}, {"name": "generate-webpack-config", "duration": 1353695, "timestamp": 135331389621, "id": 74, "parentId": 72, "tags": {}, "startTime": 1752044645506, "traceId": "b6649d4189b87d9e"}, {"name": "make", "duration": 1866, "timestamp": 135333198745, "id": 76, "parentId": 75, "tags": {}, "startTime": 1752044647315, "traceId": "b6649d4189b87d9e"}, {"name": "chunk-graph", "duration": 1441, "timestamp": 135333208407, "id": 78, "parentId": 77, "tags": {}, "startTime": 1752044647324, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-modules", "duration": 114, "timestamp": 135333210150, "id": 80, "parentId": 77, "tags": {}, "startTime": 1752044647326, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-chunks", "duration": 4827, "timestamp": 135333210485, "id": 81, "parentId": 77, "tags": {}, "startTime": 1752044647326, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-tree", "duration": 276, "timestamp": 135333215509, "id": 82, "parentId": 77, "tags": {}, "startTime": 1752044647331, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-chunk-modules", "duration": 1213, "timestamp": 135333216297, "id": 83, "parentId": 77, "tags": {}, "startTime": 1752044647332, "traceId": "b6649d4189b87d9e"}, {"name": "optimize", "duration": 7717, "timestamp": 135333210013, "id": 79, "parentId": 77, "tags": {}, "startTime": 1752044647326, "traceId": "b6649d4189b87d9e"}, {"name": "module-hash", "duration": 222, "timestamp": 135333220352, "id": 84, "parentId": 77, "tags": {}, "startTime": 1752044647336, "traceId": "b6649d4189b87d9e"}, {"name": "code-generation", "duration": 624, "timestamp": 135333220677, "id": 85, "parentId": 77, "tags": {}, "startTime": 1752044647337, "traceId": "b6649d4189b87d9e"}, {"name": "hash", "duration": 1068, "timestamp": 135333222635, "id": 86, "parentId": 77, "tags": {}, "startTime": 1752044647339, "traceId": "b6649d4189b87d9e"}, {"name": "code-generation-jobs", "duration": 516, "timestamp": 135333223694, "id": 87, "parentId": 77, "tags": {}, "startTime": 1752044647340, "traceId": "b6649d4189b87d9e"}, {"name": "module-assets", "duration": 323, "timestamp": 135333224076, "id": 88, "parentId": 77, "tags": {}, "startTime": 1752044647340, "traceId": "b6649d4189b87d9e"}, {"name": "create-chunk-assets", "duration": 484, "timestamp": 135333224436, "id": 89, "parentId": 77, "tags": {}, "startTime": 1752044647340, "traceId": "b6649d4189b87d9e"}, {"name": "minify-js", "duration": 664, "timestamp": 135333257451, "id": 91, "parentId": 90, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1752044647373, "traceId": "b6649d4189b87d9e"}, {"name": "minify-webpack-plugin-optimize", "duration": 7455, "timestamp": 135333250698, "id": 90, "parentId": 75, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1752044647367, "traceId": "b6649d4189b87d9e"}, {"name": "css-minimizer-plugin", "duration": 456, "timestamp": 135333258511, "id": 92, "parentId": 75, "tags": {}, "startTime": 1752044647374, "traceId": "b6649d4189b87d9e"}, {"name": "seal", "duration": 67750, "timestamp": 135333206818, "id": 77, "parentId": 75, "tags": {}, "startTime": 1752044647323, "traceId": "b6649d4189b87d9e"}, {"name": "webpack-compilation", "duration": 96437, "timestamp": 135333178962, "id": 75, "parentId": 72, "tags": {"name": "edge-server"}, "startTime": 1752044647295, "traceId": "b6649d4189b87d9e"}, {"name": "emit", "duration": 13433, "timestamp": 135333276294, "id": 93, "parentId": 72, "tags": {}, "startTime": 1752044647392, "traceId": "b6649d4189b87d9e"}, {"name": "webpack-close", "duration": 25877, "timestamp": 135333300937, "id": 94, "parentId": 72, "tags": {"name": "edge-server"}, "startTime": 1752044647417, "traceId": "b6649d4189b87d9e"}, {"name": "webpack-generate-error-stats", "duration": 27976, "timestamp": 135333327028, "id": 95, "parentId": 94, "tags": {}, "startTime": 1752044647443, "traceId": "b6649d4189b87d9e"}, {"name": "run-webpack-compiler", "duration": 2040461, "timestamp": 135331314958, "id": 72, "parentId": 71, "tags": {}, "startTime": 1752044645431, "traceId": "b6649d4189b87d9e"}, {"name": "format-webpack-messages", "duration": 301, "timestamp": 135333355438, "id": 96, "parentId": 71, "tags": {}, "startTime": 1752044647471, "traceId": "b6649d4189b87d9e"}, {"name": "worker-main-edge-server", "duration": 2045959, "timestamp": 135331310237, "id": 71, "parentId": 1, "tags": {}, "startTime": 1752044645426, "traceId": "b6649d4189b87d9e"}, {"name": "create-entrypoints", "duration": 48508, "timestamp": 135336236160, "id": 99, "parentId": 97, "tags": {}, "startTime": 1752044650352, "traceId": "b6649d4189b87d9e"}, {"name": "generate-webpack-config", "duration": 1040427, "timestamp": 135336285020, "id": 100, "parentId": 98, "tags": {}, "startTime": 1752044650401, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 710119, "timestamp": 135337675510, "id": 106, "parentId": 102, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1752044651792, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 755838, "timestamp": 135337675835, "id": 111, "parentId": 102, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752044651792, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 756070, "timestamp": 135337675698, "id": 107, "parentId": 102, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1752044651792, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 756040, "timestamp": 135337675790, "id": 109, "parentId": 102, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1752044651792, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 809900, "timestamp": 135337675755, "id": 108, "parentId": 102, "tags": {"request": "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1752044651792, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 819947, "timestamp": 135337675453, "id": 105, "parentId": 102, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752044651792, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 827294, "timestamp": 135337675382, "id": 104, "parentId": 102, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1752044651792, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 1581349, "timestamp": 135337674417, "id": 103, "parentId": 102, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1752044651791, "traceId": "b6649d4189b87d9e"}, {"name": "postcss-process", "duration": 223750, "timestamp": 135339212055, "id": 115, "parentId": 114, "tags": {}, "startTime": 1752044653328, "traceId": "b6649d4189b87d9e"}, {"name": "postcss-loader", "duration": 1022688, "timestamp": 135338413315, "id": 114, "parentId": 113, "tags": {}, "startTime": 1752044652530, "traceId": "b6649d4189b87d9e"}, {"name": "css-loader", "duration": 96694, "timestamp": 135339436542, "id": 116, "parentId": 113, "tags": {"astUsed": "true"}, "startTime": 1752044653553, "traceId": "b6649d4189b87d9e"}, {"name": "build-module-css", "duration": 1258637, "timestamp": 135338314936, "id": 113, "parentId": 112, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\globals.css", "layer": null}, "startTime": 1752044652431, "traceId": "b6649d4189b87d9e"}, {"name": "build-module-css", "duration": 1416406, "timestamp": 135338190522, "id": 112, "parentId": 101, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\globals.css", "layer": "app-pages-browser"}, "startTime": 1752044652307, "traceId": "b6649d4189b87d9e"}, {"name": "build-module", "duration": 261, "timestamp": 135339607272, "id": 117, "parentId": 112, "tags": {}, "startTime": 1752044653723, "traceId": "b6649d4189b87d9e"}, {"name": "add-entry", "duration": 1931781, "timestamp": 135337675812, "id": 110, "parentId": 102, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMeet%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwhatif%5C%5Cwhatif%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752044651792, "traceId": "b6649d4189b87d9e"}, {"name": "make", "duration": 1934557, "timestamp": 135337673396, "id": 102, "parentId": 101, "tags": {}, "startTime": 1752044651790, "traceId": "b6649d4189b87d9e"}, {"name": "chunk-graph", "duration": 17216, "timestamp": 135339648743, "id": 119, "parentId": 118, "tags": {}, "startTime": 1752044653765, "traceId": "b6649d4189b87d9e"}, {"name": "optimize-modules", "duration": 67, "timestamp": 135339666213, "id": 121, "parentId": 118, "tags": {}, "startTime": 1752044653782, "traceId": "b6649d4189b87d9e"}]