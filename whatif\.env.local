# What If Investment Analysis Tool - Local Environment Variables
# Copy from .env.example and fill in your actual values

# Angel One API Configuration
NEXT_PUBLIC_ANGEL_ONE_API_URL=https://apiconnect.angelone.in
ANGEL_ONE_API_KEY=TU9sOEpR
ANGEL_ONE_CLIENT_ID=M834963
ANGEL_ONE_PASSWORD=3318
ANGEL_ONE_TOTP_SECRET=CRAFUYSVQVWTWPHVWZ55KV5VJI

# Application Configuration
NEXT_PUBLIC_APP_NAME="What If"
NEXT_PUBLIC_APP_VERSION=1.0.0
NODE_ENV=development

# Security
NEXTAUTH_SECRET=development_secret_change_in_production
NEXTAUTH_URL=http://localhost:3000

# Database (if needed for storing user scenarios)
DATABASE_URL=

# External APIs (for benchmark data)
GOLD_API_KEY=
NIFTY_API_KEY=

# Analytics (optional)
GOOGLE_ANALYTICS_ID=

# Logging
LOG_LEVEL=info
ENABLE_API_LOGGING=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_REQUESTS_PER_SECOND=10
