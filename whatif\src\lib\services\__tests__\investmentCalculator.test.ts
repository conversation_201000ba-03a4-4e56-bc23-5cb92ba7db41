import { InvestmentCalculator } from '../investmentCalculator';
import { StockDataService } from '../stockData';
import { BenchmarkDataService } from '../benchmarkData';
import { InvestmentScenario, InvestmentResult } from '../../types';

// Mock the services
jest.mock('../stockData');
jest.mock('../benchmarkData');

describe('InvestmentCalculator', () => {
  let investmentCalculator: InvestmentCalculator;
  let mockStockDataService: jest.Mocked<StockDataService>;
  let mockBenchmarkDataService: jest.Mocked<BenchmarkDataService>;

  beforeEach(() => {
    mockStockDataService = new StockDataService({} as any) as jest.Mocked<StockDataService>;
    mockBenchmarkDataService = new BenchmarkDataService({} as any) as jest.Mocked<BenchmarkDataService>;
    
    investmentCalculator = new InvestmentCalculator(
      mockStockDataService,
      mockBenchmarkDataService
    );
  });

  describe('createScenario', () => {
    it('should create a valid investment scenario', () => {
      const scenario = investmentCalculator.createScenario(
        'SBIN-EQ',
        100000,
        new Date('2023-01-01'),
        new Date('2023-12-31')
      );

      expect(scenario.stockSymbol).toBe('SBIN-EQ');
      expect(scenario.investmentAmount).toBe(100000);
      expect(scenario.startDate).toEqual(new Date('2023-01-01'));
      expect(scenario.endDate).toEqual(new Date('2023-12-31'));
      expect(scenario.id).toBeDefined();
      expect(scenario.createdAt).toBeInstanceOf(Date);
    });

    it('should reject invalid investment amount', () => {
      expect(() => {
        investmentCalculator.createScenario(
          'SBIN-EQ',
          0,
          new Date('2023-01-01'),
          new Date('2023-12-31')
        );
      }).toThrow('Investment amount must be greater than 0');
    });

    it('should reject invalid date range', () => {
      expect(() => {
        investmentCalculator.createScenario(
          'SBIN-EQ',
          100000,
          new Date('2023-12-31'),
          new Date('2023-01-01')
        );
      }).toThrow('Start date must be before end date');
    });
  });

  describe('calculateInvestmentResult', () => {
    const mockScenario: InvestmentScenario = {
      id: 'test-scenario',
      stockSymbol: 'SBIN-EQ',
      investmentAmount: 100000,
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      createdAt: new Date(),
    };

    const mockResult: InvestmentResult = {
      scenario: mockScenario,
      initialValue: 100000,
      currentValue: 120000,
      absoluteReturn: 20,
      cagr: 20,
      totalReturn: 20000,
      annualizedReturn: 20,
    };

    it('should calculate investment result successfully', async () => {
      mockStockDataService.calculateInvestmentResult.mockResolvedValue(mockResult);

      const result = await investmentCalculator.calculateInvestmentResult(mockScenario);

      expect(result).toEqual(mockResult);
      expect(mockStockDataService.calculateInvestmentResult).toHaveBeenCalledWith(mockScenario);
    });

    it('should handle calculation errors', async () => {
      mockStockDataService.calculateInvestmentResult.mockRejectedValue(new Error('Calculation failed'));

      await expect(
        investmentCalculator.calculateInvestmentResult(mockScenario)
      ).rejects.toThrow('Calculation failed');
    });
  });

  describe('calculateWithComparisons', () => {
    const mockScenario: InvestmentScenario = {
      id: 'test-scenario',
      stockSymbol: 'SBIN-EQ',
      investmentAmount: 100000,
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      createdAt: new Date(),
    };

    const mockInvestmentResult: InvestmentResult = {
      scenario: mockScenario,
      initialValue: 100000,
      currentValue: 120000,
      absoluteReturn: 20,
      cagr: 20,
      totalReturn: 20000,
      annualizedReturn: 20,
    };

    const mockBenchmarkReturns = {
      GOLD: {
        initialValue: 100000,
        currentValue: 110000,
        cagr: 10,
        absoluteReturn: 10,
      },
      FD: {
        initialValue: 100000,
        currentValue: 106500,
        cagr: 6.5,
        absoluteReturn: 6.5,
      },
      NIFTY: {
        initialValue: 100000,
        currentValue: 115000,
        cagr: 15,
        absoluteReturn: 15,
      },
    };

    it('should calculate investment with benchmark comparisons', async () => {
      mockStockDataService.calculateInvestmentResult.mockResolvedValue(mockInvestmentResult);
      mockBenchmarkDataService.getAllBenchmarkData.mockResolvedValue(mockBenchmarkReturns);

      const result = await investmentCalculator.calculateWithComparisons(mockScenario);

      expect(result.investment).toEqual(mockInvestmentResult);
      expect(result.benchmarks).toEqual(mockBenchmarkReturns);
      expect(mockBenchmarkDataService.getAllBenchmarkData).toHaveBeenCalledWith(
        mockScenario.investmentAmount,
        mockScenario.startDate,
        mockScenario.endDate
      );
    });
  });

  describe('calculateSensitivityAnalysis', () => {
    const mockResult: InvestmentResult = {
      scenario: {} as InvestmentScenario,
      initialValue: 50000,
      currentValue: 60000,
      absoluteReturn: 20,
      cagr: 20,
      totalReturn: 10000,
      annualizedReturn: 20,
    };

    it('should calculate sensitivity analysis for different amounts', async () => {
      mockStockDataService.calculateInvestmentResult.mockResolvedValue(mockResult);

      const results = await investmentCalculator.calculateSensitivityAnalysis(
        'SBIN-EQ',
        100000,
        new Date('2023-01-01'),
        new Date('2023-12-31'),
        [0.5, 1, 1.5]
      );

      expect(results).toHaveLength(3);
      expect(results[0].amount).toBe(50000);
      expect(results[1].amount).toBe(100000);
      expect(results[2].amount).toBe(150000);
      expect(mockStockDataService.calculateInvestmentResult).toHaveBeenCalledTimes(3);
    });
  });

  describe('calculateTimeBasedAnalysis', () => {
    const mockResult: InvestmentResult = {
      scenario: {} as InvestmentScenario,
      initialValue: 100000,
      currentValue: 120000,
      absoluteReturn: 20,
      cagr: 20,
      totalReturn: 20000,
      annualizedReturn: 20,
    };

    it('should calculate returns for different time periods', async () => {
      mockStockDataService.calculateInvestmentResult.mockResolvedValue(mockResult);

      const results = await investmentCalculator.calculateTimeBasedAnalysis(
        'SBIN-EQ',
        100000,
        new Date('2020-01-01'), // Far enough in past to allow multiple periods
        [
          { label: '1 year', months: 12 },
          { label: '2 years', months: 24 },
        ]
      );

      expect(results.length).toBeGreaterThan(0);
      expect(results[0].period).toBe('1 year');
      expect(results[0].result).toEqual(mockResult);
    });
  });

  describe('calculateSIPReturns', () => {
    it('should calculate SIP returns correctly', async () => {
      // Mock price data for different dates
      mockStockDataService.getPriceAtDate
        .mockResolvedValueOnce({ price: 100, actualDate: new Date('2023-01-01') })
        .mockResolvedValueOnce({ price: 105, actualDate: new Date('2023-02-01') })
        .mockResolvedValueOnce({ price: 110, actualDate: new Date('2023-03-01') });

      mockStockDataService.getCurrentPrice.mockResolvedValue({
        symbol: 'SBIN-EQ',
        name: 'SBIN-EQ',
        exchange: 'NSE',
        token: 'SBIN-EQ',
        currentPrice: 120,
        lastUpdated: new Date(),
      });

      const result = await investmentCalculator.calculateSIPReturns(
        'SBIN-EQ',
        10000,
        new Date('2023-01-01'),
        new Date('2023-03-01')
      );

      expect(result.totalInvested).toBeGreaterThan(0);
      expect(result.currentValue).toBeGreaterThan(0);
      expect(result.installments.length).toBeGreaterThan(0);
      expect(result.cagr).toBeDefined();
      expect(result.absoluteReturn).toBeDefined();
    });

    it('should handle no installments case', async () => {
      mockStockDataService.getPriceAtDate.mockResolvedValue(null);

      await expect(
        investmentCalculator.calculateSIPReturns(
          'SBIN-EQ',
          10000,
          new Date('2023-01-01'),
          new Date('2023-03-01')
        )
      ).rejects.toThrow('No SIP installments could be calculated');
    });
  });

  describe('calculateOptimalTiming', () => {
    const mockInvestmentResult: InvestmentResult = {
      scenario: {} as InvestmentScenario,
      initialValue: 100000,
      currentValue: 120000,
      absoluteReturn: 20,
      cagr: 20,
      totalReturn: 20000,
      annualizedReturn: 20,
    };

    const mockSIPResult = {
      totalInvested: 100000,
      currentValue: 115000,
      totalReturn: 15000,
      cagr: 15,
      absoluteReturn: 15,
      installments: [],
    };

    it('should compare different investment strategies', async () => {
      mockStockDataService.calculateInvestmentResult.mockResolvedValue(mockInvestmentResult);
      mockStockDataService.getPriceAtDate.mockResolvedValue({ price: 100, actualDate: new Date() });
      mockStockDataService.getCurrentPrice.mockResolvedValue({
        symbol: 'SBIN-EQ',
        name: 'SBIN-EQ',
        exchange: 'NSE',
        token: 'SBIN-EQ',
        currentPrice: 120,
        lastUpdated: new Date(),
      });

      const results = await investmentCalculator.calculateOptimalTiming(
        'SBIN-EQ',
        100000,
        new Date('2022-01-01'),
        new Date('2023-01-01'),
        [
          { name: 'Lump Sum', type: 'lump_sum' },
          { name: 'Monthly SIP', type: 'monthly_sip' },
        ]
      );

      expect(results.length).toBeGreaterThan(0);
      expect(results[0].strategy).toBe('Lump Sum');
      expect(results[0].totalInvested).toBe(100000);
      expect(results[0].currentValue).toBe(120000);
    });
  });
});
