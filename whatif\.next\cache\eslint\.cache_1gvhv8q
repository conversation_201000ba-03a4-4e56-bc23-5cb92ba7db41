[{"C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\config\\index.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\types\\index.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\utils\\index.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\api\\angelone.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\security\\index.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\security\\__tests__\\index.test.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\benchmarkData.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\comparisonService.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\investmentCalculator.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\stockData.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\comparisonService.test.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\investmentCalculator.test.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\stockData.test.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\utils\\__tests__\\index.test.ts": "16"}, {"size": 689, "mtime": 1752033350525, "results": "17", "hashOfConfig": "18"}, {"size": 4086, "mtime": 1752033359604, "results": "19", "hashOfConfig": "18"}, {"size": 2870, "mtime": 1752033747000, "results": "20", "hashOfConfig": "18"}, {"size": 2809, "mtime": 1752035040723, "results": "21", "hashOfConfig": "18"}, {"size": 4761, "mtime": 1752033992445, "results": "22", "hashOfConfig": "18"}, {"size": 8156, "mtime": 1752044611165, "results": "23", "hashOfConfig": "18"}, {"size": 5004, "mtime": 1752034825140, "results": "24", "hashOfConfig": "18"}, {"size": 5318, "mtime": 1752034852146, "results": "25", "hashOfConfig": "18"}, {"size": 8019, "mtime": 1752035386459, "results": "26", "hashOfConfig": "18"}, {"size": 12402, "mtime": 1752044171703, "results": "27", "hashOfConfig": "18"}, {"size": 10891, "mtime": 1752035442986, "results": "28", "hashOfConfig": "18"}, {"size": 6756, "mtime": 1752035162716, "results": "29", "hashOfConfig": "18"}, {"size": 8928, "mtime": 1752044293208, "results": "30", "hashOfConfig": "18"}, {"size": 9930, "mtime": 1752044241523, "results": "31", "hashOfConfig": "18"}, {"size": 7261, "mtime": 1752035274876, "results": "32", "hashOfConfig": "18"}, {"size": 4688, "mtime": 1752034708641, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10d6ury", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\config\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\api\\angelone.ts", ["82"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\security\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\security\\__tests__\\index.test.ts", ["83", "84", "85", "86"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\benchmarkData.ts", ["87", "88", "89"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\comparisonService.ts", ["90", "91", "92", "93"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\investmentCalculator.ts", ["94"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\stockData.ts", ["95"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\comparisonService.test.ts", ["96", "97", "98", "99"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\investmentCalculator.test.ts", ["100", "101", "102"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\stockData.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\utils\\__tests__\\index.test.ts", ["103"], [], {"ruleId": "104", "severity": 2, "message": "105", "line": 91, "column": 20, "nodeType": null, "messageId": "106", "endLine": 91, "endColumn": 32}, {"ruleId": "107", "severity": 2, "message": "108", "line": 81, "column": 33, "nodeType": "109", "messageId": "110", "endLine": 81, "endColumn": 36, "suggestions": "111"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 82, "column": 33, "nodeType": "109", "messageId": "110", "endLine": 82, "endColumn": 36, "suggestions": "112"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 83, "column": 35, "nodeType": "109", "messageId": "110", "endLine": 83, "endColumn": 38, "suggestions": "113"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 84, "column": 35, "nodeType": "109", "messageId": "110", "endLine": 84, "endColumn": 38, "suggestions": "114"}, {"ruleId": "104", "severity": 2, "message": "115", "line": 4, "column": 28, "nodeType": null, "messageId": "106", "endLine": 4, "endColumn": 46}, {"ruleId": "104", "severity": 2, "message": "116", "line": 6, "column": 25, "nodeType": null, "messageId": "106", "endLine": 6, "endColumn": 40}, {"ruleId": "107", "severity": 2, "message": "108", "line": 208, "column": 20, "nodeType": "109", "messageId": "110", "endLine": 208, "endColumn": 23, "suggestions": "117"}, {"ruleId": "104", "severity": 2, "message": "118", "line": 8, "column": 3, "nodeType": null, "messageId": "106", "endLine": 8, "endColumn": 19}, {"ruleId": "104", "severity": 2, "message": "119", "line": 10, "column": 3, "nodeType": null, "messageId": "106", "endLine": 10, "endColumn": 17}, {"ruleId": "107", "severity": 2, "message": "108", "line": 237, "column": 17, "nodeType": "109", "messageId": "110", "endLine": 237, "endColumn": 20, "suggestions": "120"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 238, "column": 17, "nodeType": "109", "messageId": "110", "endLine": 238, "endColumn": 20, "suggestions": "121"}, {"ruleId": "104", "severity": 2, "message": "116", "line": 16, "column": 3, "nodeType": null, "messageId": "106", "endLine": 16, "endColumn": 18}, {"ruleId": "104", "severity": 2, "message": "122", "line": 3, "column": 18, "nodeType": null, "messageId": "106", "endLine": 3, "endColumn": 26}, {"ruleId": "104", "severity": 2, "message": "118", "line": 4, "column": 30, "nodeType": null, "messageId": "106", "endLine": 4, "endColumn": 46}, {"ruleId": "107", "severity": 2, "message": "108", "line": 16, "column": 63, "nodeType": "109", "messageId": "110", "endLine": 16, "endColumn": 66, "suggestions": "123"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 16, "column": 74, "nodeType": "109", "messageId": "110", "endLine": 16, "endColumn": 77, "suggestions": "124"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 17, "column": 63, "nodeType": "109", "messageId": "110", "endLine": 17, "endColumn": 66, "suggestions": "125"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 16, "column": 55, "nodeType": "109", "messageId": "110", "endLine": 16, "endColumn": 58, "suggestions": "126"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 17, "column": 63, "nodeType": "109", "messageId": "110", "endLine": 17, "endColumn": 66, "suggestions": "127"}, {"ruleId": "104", "severity": 2, "message": "128", "line": 276, "column": 11, "nodeType": null, "messageId": "106", "endLine": 276, "endColumn": 24}, {"ruleId": "104", "severity": 2, "message": "129", "line": 10, "column": 3, "nodeType": null, "messageId": "106", "endLine": 10, "endColumn": 15}, "@typescript-eslint/no-unused-vars", "'refreshError' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["130", "131"], ["132", "133"], ["134", "135"], ["136", "137"], "'CALCULATION_CONFIG' is defined but never used.", "'HistoricalPrice' is defined but never used.", ["138", "139"], "'InvestmentResult' is defined but never used.", "'ChartDataPoint' is defined but never used.", ["140", "141"], ["142", "143"], "'parseISO' is defined but never used.", ["144", "145"], ["146", "147"], ["148", "149"], ["150", "151"], ["152", "153"], "'mockSIPResult' is assigned a value but never used.", "'isMarketOpen' is defined but never used.", {"messageId": "154", "fix": "155", "desc": "156"}, {"messageId": "157", "fix": "158", "desc": "159"}, {"messageId": "154", "fix": "160", "desc": "156"}, {"messageId": "157", "fix": "161", "desc": "159"}, {"messageId": "154", "fix": "162", "desc": "156"}, {"messageId": "157", "fix": "163", "desc": "159"}, {"messageId": "154", "fix": "164", "desc": "156"}, {"messageId": "157", "fix": "165", "desc": "159"}, {"messageId": "154", "fix": "166", "desc": "156"}, {"messageId": "157", "fix": "167", "desc": "159"}, {"messageId": "154", "fix": "168", "desc": "156"}, {"messageId": "157", "fix": "169", "desc": "159"}, {"messageId": "154", "fix": "170", "desc": "156"}, {"messageId": "157", "fix": "171", "desc": "159"}, {"messageId": "154", "fix": "172", "desc": "156"}, {"messageId": "157", "fix": "173", "desc": "159"}, {"messageId": "154", "fix": "174", "desc": "156"}, {"messageId": "157", "fix": "175", "desc": "159"}, {"messageId": "154", "fix": "176", "desc": "156"}, {"messageId": "157", "fix": "177", "desc": "159"}, {"messageId": "154", "fix": "178", "desc": "156"}, {"messageId": "157", "fix": "179", "desc": "159"}, {"messageId": "154", "fix": "180", "desc": "156"}, {"messageId": "157", "fix": "181", "desc": "159"}, "suggestUnknown", {"range": "182", "text": "183"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "184", "text": "185"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "186", "text": "183"}, {"range": "187", "text": "185"}, {"range": "188", "text": "183"}, {"range": "189", "text": "185"}, {"range": "190", "text": "183"}, {"range": "191", "text": "185"}, {"range": "192", "text": "183"}, {"range": "193", "text": "185"}, {"range": "194", "text": "183"}, {"range": "195", "text": "185"}, {"range": "196", "text": "183"}, {"range": "197", "text": "185"}, {"range": "198", "text": "183"}, {"range": "199", "text": "185"}, {"range": "200", "text": "183"}, {"range": "201", "text": "185"}, {"range": "202", "text": "183"}, {"range": "203", "text": "185"}, {"range": "204", "text": "183"}, {"range": "205", "text": "185"}, {"range": "206", "text": "183"}, {"range": "207", "text": "185"}, [2372, 2375], "unknown", [2372, 2375], "never", [2429, 2432], [2429, 2432], [2495, 2498], [2495, 2498], [2562, 2565], [2562, 2565], [6565, 6568], [6565, 6568], [7118, 7121], [7118, 7121], [7139, 7142], [7139, 7142], [657, 660], [657, 660], [668, 671], [668, 671], [773, 776], [773, 776], [612, 615], [612, 615], [713, 716], [713, 716]]