[{"C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\config\\index.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\types\\index.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\utils\\index.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\api\\angelone.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\security\\index.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\security\\__tests__\\index.test.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\benchmarkData.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\comparisonService.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\investmentCalculator.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\stockData.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\comparisonService.test.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\investmentCalculator.test.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\stockData.test.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\utils\\__tests__\\index.test.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\test-api-connection.ts": "17"}, {"size": 689, "mtime": 1752033350525, "results": "18", "hashOfConfig": "19"}, {"size": 4086, "mtime": 1752033359604, "results": "20", "hashOfConfig": "19"}, {"size": 2870, "mtime": 1752033747000, "results": "21", "hashOfConfig": "19"}, {"size": 2809, "mtime": 1752035040723, "results": "22", "hashOfConfig": "19"}, {"size": 4761, "mtime": 1752033992445, "results": "23", "hashOfConfig": "19"}, {"size": 8156, "mtime": 1752044611165, "results": "24", "hashOfConfig": "19"}, {"size": 5004, "mtime": 1752034825140, "results": "25", "hashOfConfig": "19"}, {"size": 5318, "mtime": 1752034852146, "results": "26", "hashOfConfig": "19"}, {"size": 8019, "mtime": 1752035386459, "results": "27", "hashOfConfig": "19"}, {"size": 12402, "mtime": 1752044171703, "results": "28", "hashOfConfig": "19"}, {"size": 10891, "mtime": 1752035442986, "results": "29", "hashOfConfig": "19"}, {"size": 6756, "mtime": 1752035162716, "results": "30", "hashOfConfig": "19"}, {"size": 8928, "mtime": 1752044293208, "results": "31", "hashOfConfig": "19"}, {"size": 9930, "mtime": 1752044241523, "results": "32", "hashOfConfig": "19"}, {"size": 7261, "mtime": 1752035274876, "results": "33", "hashOfConfig": "19"}, {"size": 4688, "mtime": 1752034708641, "results": "34", "hashOfConfig": "19"}, {"size": 2073, "mtime": 1752045113348, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10d6ury", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\config\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\api\\angelone.ts", ["87"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\security\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\security\\__tests__\\index.test.ts", ["88", "89", "90", "91"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\benchmarkData.ts", ["92", "93", "94"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\comparisonService.ts", ["95", "96", "97", "98"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\investmentCalculator.ts", ["99"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\stockData.ts", ["100"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\comparisonService.test.ts", ["101", "102", "103", "104"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\investmentCalculator.test.ts", ["105", "106", "107"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\services\\__tests__\\stockData.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\lib\\utils\\__tests__\\index.test.ts", ["108"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\test-api-connection.ts", [], [], {"ruleId": "109", "severity": 2, "message": "110", "line": 91, "column": 20, "nodeType": null, "messageId": "111", "endLine": 91, "endColumn": 32}, {"ruleId": "112", "severity": 2, "message": "113", "line": 81, "column": 33, "nodeType": "114", "messageId": "115", "endLine": 81, "endColumn": 36, "suggestions": "116"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 82, "column": 33, "nodeType": "114", "messageId": "115", "endLine": 82, "endColumn": 36, "suggestions": "117"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 83, "column": 35, "nodeType": "114", "messageId": "115", "endLine": 83, "endColumn": 38, "suggestions": "118"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 84, "column": 35, "nodeType": "114", "messageId": "115", "endLine": 84, "endColumn": 38, "suggestions": "119"}, {"ruleId": "109", "severity": 2, "message": "120", "line": 4, "column": 28, "nodeType": null, "messageId": "111", "endLine": 4, "endColumn": 46}, {"ruleId": "109", "severity": 2, "message": "121", "line": 6, "column": 25, "nodeType": null, "messageId": "111", "endLine": 6, "endColumn": 40}, {"ruleId": "112", "severity": 2, "message": "113", "line": 208, "column": 20, "nodeType": "114", "messageId": "115", "endLine": 208, "endColumn": 23, "suggestions": "122"}, {"ruleId": "109", "severity": 2, "message": "123", "line": 8, "column": 3, "nodeType": null, "messageId": "111", "endLine": 8, "endColumn": 19}, {"ruleId": "109", "severity": 2, "message": "124", "line": 10, "column": 3, "nodeType": null, "messageId": "111", "endLine": 10, "endColumn": 17}, {"ruleId": "112", "severity": 2, "message": "113", "line": 237, "column": 17, "nodeType": "114", "messageId": "115", "endLine": 237, "endColumn": 20, "suggestions": "125"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 238, "column": 17, "nodeType": "114", "messageId": "115", "endLine": 238, "endColumn": 20, "suggestions": "126"}, {"ruleId": "109", "severity": 2, "message": "121", "line": 16, "column": 3, "nodeType": null, "messageId": "111", "endLine": 16, "endColumn": 18}, {"ruleId": "109", "severity": 2, "message": "127", "line": 3, "column": 18, "nodeType": null, "messageId": "111", "endLine": 3, "endColumn": 26}, {"ruleId": "109", "severity": 2, "message": "123", "line": 4, "column": 30, "nodeType": null, "messageId": "111", "endLine": 4, "endColumn": 46}, {"ruleId": "112", "severity": 2, "message": "113", "line": 16, "column": 63, "nodeType": "114", "messageId": "115", "endLine": 16, "endColumn": 66, "suggestions": "128"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 16, "column": 74, "nodeType": "114", "messageId": "115", "endLine": 16, "endColumn": 77, "suggestions": "129"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 17, "column": 63, "nodeType": "114", "messageId": "115", "endLine": 17, "endColumn": 66, "suggestions": "130"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 16, "column": 55, "nodeType": "114", "messageId": "115", "endLine": 16, "endColumn": 58, "suggestions": "131"}, {"ruleId": "112", "severity": 2, "message": "113", "line": 17, "column": 63, "nodeType": "114", "messageId": "115", "endLine": 17, "endColumn": 66, "suggestions": "132"}, {"ruleId": "109", "severity": 2, "message": "133", "line": 276, "column": 11, "nodeType": null, "messageId": "111", "endLine": 276, "endColumn": 24}, {"ruleId": "109", "severity": 2, "message": "134", "line": 10, "column": 3, "nodeType": null, "messageId": "111", "endLine": 10, "endColumn": 15}, "@typescript-eslint/no-unused-vars", "'refreshError' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["135", "136"], ["137", "138"], ["139", "140"], ["141", "142"], "'CALCULATION_CONFIG' is defined but never used.", "'HistoricalPrice' is defined but never used.", ["143", "144"], "'InvestmentResult' is defined but never used.", "'ChartDataPoint' is defined but never used.", ["145", "146"], ["147", "148"], "'parseISO' is defined but never used.", ["149", "150"], ["151", "152"], ["153", "154"], ["155", "156"], ["157", "158"], "'mockSIPResult' is assigned a value but never used.", "'isMarketOpen' is defined but never used.", {"messageId": "159", "fix": "160", "desc": "161"}, {"messageId": "162", "fix": "163", "desc": "164"}, {"messageId": "159", "fix": "165", "desc": "161"}, {"messageId": "162", "fix": "166", "desc": "164"}, {"messageId": "159", "fix": "167", "desc": "161"}, {"messageId": "162", "fix": "168", "desc": "164"}, {"messageId": "159", "fix": "169", "desc": "161"}, {"messageId": "162", "fix": "170", "desc": "164"}, {"messageId": "159", "fix": "171", "desc": "161"}, {"messageId": "162", "fix": "172", "desc": "164"}, {"messageId": "159", "fix": "173", "desc": "161"}, {"messageId": "162", "fix": "174", "desc": "164"}, {"messageId": "159", "fix": "175", "desc": "161"}, {"messageId": "162", "fix": "176", "desc": "164"}, {"messageId": "159", "fix": "177", "desc": "161"}, {"messageId": "162", "fix": "178", "desc": "164"}, {"messageId": "159", "fix": "179", "desc": "161"}, {"messageId": "162", "fix": "180", "desc": "164"}, {"messageId": "159", "fix": "181", "desc": "161"}, {"messageId": "162", "fix": "182", "desc": "164"}, {"messageId": "159", "fix": "183", "desc": "161"}, {"messageId": "162", "fix": "184", "desc": "164"}, {"messageId": "159", "fix": "185", "desc": "161"}, {"messageId": "162", "fix": "186", "desc": "164"}, "suggestUnknown", {"range": "187", "text": "188"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "189", "text": "190"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "191", "text": "188"}, {"range": "192", "text": "190"}, {"range": "193", "text": "188"}, {"range": "194", "text": "190"}, {"range": "195", "text": "188"}, {"range": "196", "text": "190"}, {"range": "197", "text": "188"}, {"range": "198", "text": "190"}, {"range": "199", "text": "188"}, {"range": "200", "text": "190"}, {"range": "201", "text": "188"}, {"range": "202", "text": "190"}, {"range": "203", "text": "188"}, {"range": "204", "text": "190"}, {"range": "205", "text": "188"}, {"range": "206", "text": "190"}, {"range": "207", "text": "188"}, {"range": "208", "text": "190"}, {"range": "209", "text": "188"}, {"range": "210", "text": "190"}, {"range": "211", "text": "188"}, {"range": "212", "text": "190"}, [2372, 2375], "unknown", [2372, 2375], "never", [2429, 2432], [2429, 2432], [2495, 2498], [2495, 2498], [2562, 2565], [2562, 2565], [6565, 6568], [6565, 6568], [7118, 7121], [7118, 7121], [7139, 7142], [7139, 7142], [657, 660], [657, 660], [668, 671], [668, 671], [773, 776], [773, 776], [612, 615], [612, 615], [713, 716], [713, 716]]