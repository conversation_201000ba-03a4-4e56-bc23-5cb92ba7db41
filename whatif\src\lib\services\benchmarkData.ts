// Benchmark data service for the What If investment analysis tool

import { AngelOneClient } from '../api/angelone';
import { BENCHMARK_CONFIG } from '../config';
import { calculateCAGR, calculateAbsoluteReturn, calculateYearsBetweenDates } from '../utils';
import { BenchmarkData } from '../types';

export class BenchmarkDataService {
  private angelOneClient: AngelOneClient;

  constructor(angelOneClient: AngelOneClient) {
    this.angelOneClient = angelOneClient;
  }

  /**
   * Get Nifty 50 historical data
   */
  async getNiftyData(startDate: Date, endDate: Date): Promise<BenchmarkData> {
    try {
      const historicalData = await this.angelOneClient.getHistoricalData({
        exchange: 'NSE',
        symboltoken: BENCHMARK_CONFIG.types.NIFTY.token,
        interval: 'ONE_DAY',
        fromdate: startDate.toISOString().slice(0, 16).replace('T', ' '),
        todate: endDate.toISOString().slice(0, 16).replace('T', ' '),
      });

      return {
        type: 'NIFTY',
        name: BENCHMARK_CONFIG.types.NIFTY.name,
        returns: historicalData.map(item => ({
          date: item.date,
          value: item.close,
        })),
      };
    } catch (error) {
      console.error('Error fetching Nifty data:', error);
      throw new Error(`Failed to fetch Nifty data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get gold price data (simulated - in real implementation, would use gold API)
   */
  async getGoldData(startDate: Date, endDate: Date): Promise<BenchmarkData> {
    try {
      // Simulate gold price data with realistic growth
      // In real implementation, this would call a gold price API
      const returns = this.simulateGoldPrices(startDate, endDate);

      return {
        type: 'GOLD',
        name: BENCHMARK_CONFIG.types.GOLD.name,
        returns,
      };
    } catch (error) {
      console.error('Error fetching gold data:', error);
      throw new Error(`Failed to fetch gold data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get Fixed Deposit returns (calculated based on average FD rates)
   */
  async getFDData(startDate: Date, endDate: Date): Promise<BenchmarkData> {
    try {
      const returns = this.calculateFDReturns(startDate, endDate);

      return {
        type: 'FD',
        name: BENCHMARK_CONFIG.types.FD.name,
        returns,
      };
    } catch (error) {
      console.error('Error calculating FD data:', error);
      throw new Error(`Failed to calculate FD data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate benchmark returns for a given investment amount and period
   */
  async calculateBenchmarkReturns(
    benchmarkType: 'GOLD' | 'FD' | 'NIFTY',
    investmentAmount: number,
    startDate: Date,
    endDate: Date
  ): Promise<{
    initialValue: number;
    currentValue: number;
    cagr: number;
    absoluteReturn: number;
  }> {
    let benchmarkData: BenchmarkData;

    switch (benchmarkType) {
      case 'NIFTY':
        benchmarkData = await this.getNiftyData(startDate, endDate);
        break;
      case 'GOLD':
        benchmarkData = await this.getGoldData(startDate, endDate);
        break;
      case 'FD':
        benchmarkData = await getFDData(startDate, endDate);
        break;
      default:
        throw new Error(`Unsupported benchmark type: ${benchmarkType}`);
    }

    if (benchmarkData.returns.length === 0) {
      throw new Error(`No data available for ${benchmarkType} in the specified period`);
    }

    const startValue = benchmarkData.returns[0].value;
    const endValue = benchmarkData.returns[benchmarkData.returns.length - 1].value;

    // Calculate how much of the benchmark could be bought with the investment amount
    const units = investmentAmount / startValue;
    const currentValue = units * endValue;

    const years = calculateYearsBetweenDates(startDate, endDate);
    const cagr = calculateCAGR(investmentAmount, currentValue, years);
    const absoluteReturn = calculateAbsoluteReturn(investmentAmount, currentValue);

    return {
      initialValue: investmentAmount,
      currentValue,
      cagr,
      absoluteReturn,
    };
  }

  /**
   * Simulate gold prices with realistic growth patterns
   * In production, this would be replaced with actual gold price API
   */
  private simulateGoldPrices(startDate: Date, endDate: Date): Array<{ date: Date; value: number }> {
    const returns: Array<{ date: Date; value: number }> = [];
    const currentDate = new Date(startDate);
    
    // Starting gold price (approximate INR per 10 grams)
    let currentPrice = 50000;
    
    // Gold typically grows at 8-10% annually with volatility
    const annualGrowthRate = 0.09; // 9% annual growth
    const dailyGrowthRate = Math.pow(1 + annualGrowthRate, 1/365) - 1;

    while (currentDate <= endDate) {
      // Add some random volatility (-2% to +2% daily)
      const volatility = (Math.random() - 0.5) * 0.04;
      const dailyChange = dailyGrowthRate + volatility;
      
      currentPrice *= (1 + dailyChange);
      
      returns.push({
        date: new Date(currentDate),
        value: Math.round(currentPrice * 100) / 100, // Round to 2 decimal places
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return returns;
  }

  /**
   * Calculate FD returns based on compound interest
   */
  private calculateFDReturns(startDate: Date, endDate: Date): Array<{ date: Date; value: number }> {
    const returns: Array<{ date: Date; value: number }> = [];
    const currentDate = new Date(startDate);
    
    const fdRate = BENCHMARK_CONFIG.types.FD.defaultRate / 100; // Convert percentage to decimal
    const dailyRate = fdRate / 365; // Daily compound rate
    
    let currentValue = 100; // Starting with base value of 100

    while (currentDate <= endDate) {
      currentValue *= (1 + dailyRate);
      
      returns.push({
        date: new Date(currentDate),
        value: Math.round(currentValue * 100) / 100,
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return returns;
  }

  /**
   * Get all benchmark data for comparison
   */
  async getAllBenchmarkData(
    investmentAmount: number,
    startDate: Date,
    endDate: Date
  ): Promise<{
    [key: string]: {
      initialValue: number;
      currentValue: number;
      cagr: number;
      absoluteReturn: number;
    };
  }> {
    const benchmarks = ['GOLD', 'FD', 'NIFTY'] as const;
    const results: Record<string, {
      initialValue: number;
      currentValue: number;
      cagr: number;
      absoluteReturn: number;
    }> = {};

    // Process benchmarks sequentially to avoid overwhelming the API
    for (const benchmark of benchmarks) {
      try {
        results[benchmark] = await this.calculateBenchmarkReturns(
          benchmark,
          investmentAmount,
          startDate,
          endDate
        );
      } catch (error) {
        console.error(`Error calculating ${benchmark} returns:`, error);
        // Continue with other benchmarks even if one fails
        results[benchmark] = {
          initialValue: investmentAmount,
          currentValue: investmentAmount,
          cagr: 0,
          absoluteReturn: 0,
        };
      }
    }

    return results;
  }
}

// Helper function to maintain compatibility
async function getFDData(startDate: Date, endDate: Date): Promise<BenchmarkData> {
  // This is a temporary implementation - in a real service, this would be a method
  const returns: Array<{ date: Date; value: number }> = [];
  const currentDate = new Date(startDate);
  
  const fdRate = BENCHMARK_CONFIG.types.FD.defaultRate / 100;
  const dailyRate = fdRate / 365;
  
  let currentValue = 100;

  while (currentDate <= endDate) {
    currentValue *= (1 + dailyRate);
    
    returns.push({
      date: new Date(currentDate),
      value: Math.round(currentValue * 100) / 100,
    });

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return {
    type: 'FD',
    name: BENCHMARK_CONFIG.types.FD.name,
    returns,
  };
}
