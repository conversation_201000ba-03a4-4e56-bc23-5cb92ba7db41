// Simple test for Angel One API
const axios = require('axios');
const { authenticator } = require('otplib');

const API_KEY = 'TU9sOEpR';
const CLIENT_ID = 'M834963';
const PASSWORD = '3318';
const TOTP_SECRET = 'CRAFUYSVQVWTWPHVWZ55KV5VJI';

async function testAPI() {
  console.log('🔄 Testing Angel One API...');
  
  try {
    // Generate TOTP
    const totp = authenticator.generate(TOTP_SECRET);
    console.log('📱 TOTP generated:', totp);

    // Login
    const loginResponse = await axios.post('https://apiconnect.angelone.in/rest/auth/angelbroking/user/v1/loginByPassword', {
      clientcode: CLIENT_ID,
      password: PASSWORD,
      totp: totp
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-UserType': 'USER',
        'X-SourceID': 'WEB',
        'X-ClientLocalIP': '127.0.0.1',
        'X-ClientPublicIP': '127.0.0.1',
        'X-MACAddress': '00:00:00:00:00:00',
        'X-PrivateKey': API_KEY,
      }
    });

    console.log('✅ Login response:', loginResponse.data);

    if (loginResponse.data.status && loginResponse.data.data) {
      const jwtToken = loginResponse.data.data.jwtToken;
      console.log('🎉 Login successful! JWT token received.');

      // Test historical data
      console.log('\n📈 Testing historical data...');
      const historicalResponse = await axios.post('https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData', {
        exchange: 'NSE',
        symboltoken: '3045',
        interval: 'ONE_DAY',
        fromdate: '2024-01-01 09:15',
        todate: '2024-01-31 15:30'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': API_KEY,
          'Authorization': `Bearer ${jwtToken}`
        }
      });

      console.log('📊 Historical data response:', historicalResponse.data);

      // Test LTP
      console.log('\n💰 Testing LTP...');
      const ltpResponse = await axios.post('https://apiconnect.angelone.in/rest/secure/angelbroking/order/v1/getLtpData', {
        exchange: 'NSE',
        tradingsymbol: 'SBIN-EQ',
        symboltoken: '3045'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': API_KEY,
          'Authorization': `Bearer ${jwtToken}`
        }
      });

      console.log('💵 LTP response:', ltpResponse.data);

    } else {
      console.log('❌ Login failed:', loginResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response ? error.response.data : error.message);
  }
}

testAPI();
