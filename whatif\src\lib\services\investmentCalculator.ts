// Investment calculation engine for the What If investment analysis tool

import { StockDataService } from './stockData';
import { BenchmarkDataService } from './benchmarkData';
import { 
  calculateCAGR, 
  calculateAbsoluteReturn, 
  calculateYearsBetweenDates,
  validateDateRange,
  generateId 
} from '../utils';
import {
  InvestmentScenario,
  InvestmentResult,
  ComparisonResult,
  HistoricalPrice,
} from '../types';

export class InvestmentCalculator {
  private stockDataService: StockDataService;
  private benchmarkDataService: BenchmarkDataService;

  constructor(
    stockDataService: StockDataService,
    benchmarkDataService: BenchmarkDataService
  ) {
    this.stockDataService = stockDataService;
    this.benchmarkDataService = benchmarkDataService;
  }

  /**
   * Create a new investment scenario
   */
  createScenario(
    stockSymbol: string,
    investmentAmount: number,
    startDate: Date,
    endDate: Date = new Date()
  ): InvestmentScenario {
    // Validate inputs
    if (investmentAmount <= 0) {
      throw new Error('Investment amount must be greater than 0');
    }

    const validation = validateDateRange(startDate, endDate);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    return {
      id: generateId(),
      stockSymbol,
      investmentAmount,
      startDate,
      endDate,
      createdAt: new Date(),
    };
  }

  /**
   * Calculate investment result for a scenario
   */
  async calculateInvestmentResult(scenario: InvestmentScenario): Promise<InvestmentResult> {
    try {
      return await this.stockDataService.calculateInvestmentResult(scenario);
    } catch (error) {
      console.error('Error calculating investment result:', error);
      throw error;
    }
  }

  /**
   * Calculate investment result with benchmark comparisons
   */
  async calculateWithComparisons(scenario: InvestmentScenario): Promise<ComparisonResult> {
    try {
      // Calculate the main investment result
      const investmentResult = await this.calculateInvestmentResult(scenario);

      // Calculate benchmark returns
      const benchmarkReturns = await this.benchmarkDataService.getAllBenchmarkData(
        scenario.investmentAmount,
        scenario.startDate,
        scenario.endDate
      );

      return {
        investment: investmentResult,
        benchmarks: benchmarkReturns,
      };
    } catch (error) {
      console.error('Error calculating investment with comparisons:', error);
      throw error;
    }
  }

  /**
   * Calculate returns for multiple investment amounts (sensitivity analysis)
   */
  async calculateSensitivityAnalysis(
    stockSymbol: string,
    baseAmount: number,
    startDate: Date,
    endDate: Date,
    variations: number[] = [0.5, 0.75, 1, 1.25, 1.5, 2] // Multipliers
  ): Promise<Array<{
    amount: number;
    result: InvestmentResult;
  }>> {
    const results: Array<{ amount: number; result: InvestmentResult }> = [];

    for (const multiplier of variations) {
      const amount = baseAmount * multiplier;
      const scenario = this.createScenario(stockSymbol, amount, startDate, endDate);
      
      try {
        const result = await this.calculateInvestmentResult(scenario);
        results.push({ amount, result });
      } catch (error) {
        console.error(`Error calculating for amount ${amount}:`, error);
        // Continue with other amounts
      }
    }

    return results;
  }

  /**
   * Calculate returns for different time periods (time-based analysis)
   */
  async calculateTimeBasedAnalysis(
    stockSymbol: string,
    investmentAmount: number,
    baseStartDate: Date,
    periods: Array<{ label: string; months: number }> = [
      { label: '6 months', months: 6 },
      { label: '1 year', months: 12 },
      { label: '2 years', months: 24 },
      { label: '3 years', months: 36 },
      { label: '5 years', months: 60 },
    ]
  ): Promise<Array<{
    period: string;
    startDate: Date;
    endDate: Date;
    result: InvestmentResult;
  }>> {
    const results: Array<{
      period: string;
      startDate: Date;
      endDate: Date;
      result: InvestmentResult;
    }> = [];

    for (const period of periods) {
      const startDate = new Date(baseStartDate);
      const endDate = new Date(baseStartDate);
      endDate.setMonth(endDate.getMonth() + period.months);

      // Don't calculate for future dates
      if (endDate > new Date()) {
        continue;
      }

      const scenario = this.createScenario(stockSymbol, investmentAmount, startDate, endDate);
      
      try {
        const result = await this.calculateInvestmentResult(scenario);
        results.push({
          period: period.label,
          startDate,
          endDate,
          result,
        });
      } catch (error) {
        console.error(`Error calculating for period ${period.label}:`, error);
        // Continue with other periods
      }
    }

    return results;
  }

  /**
   * Calculate SIP (Systematic Investment Plan) returns
   */
  async calculateSIPReturns(
    stockSymbol: string,
    monthlyAmount: number,
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalInvested: number;
    currentValue: number;
    totalReturn: number;
    cagr: number;
    absoluteReturn: number;
    installments: Array<{
      date: Date;
      amount: number;
      price: number;
      units: number;
      cumulativeUnits: number;
      cumulativeInvestment: number;
    }>;
  }> {
    const installments: Array<{
      date: Date;
      amount: number;
      price: number;
      units: number;
      cumulativeUnits: number;
      cumulativeInvestment: number;
    }> = [];

    let totalInvested = 0;
    let totalUnits = 0;
    const currentDate = new Date(startDate);

    // Calculate monthly investments
    while (currentDate <= endDate) {
      try {
        // Get price at this date (or closest available)
        const priceData = await this.stockDataService.getPriceAtDate(
          stockSymbol,
          'NSE', // Default to NSE
          currentDate
        );

        if (priceData) {
          const units = monthlyAmount / priceData.price;
          totalUnits += units;
          totalInvested += monthlyAmount;

          installments.push({
            date: new Date(currentDate),
            amount: monthlyAmount,
            price: priceData.price,
            units,
            cumulativeUnits: totalUnits,
            cumulativeInvestment: totalInvested,
          });
        }
      } catch (error) {
        console.error(`Error calculating SIP for ${currentDate}:`, error);
        // Continue with next month
      }

      // Move to next month
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    if (installments.length === 0) {
      throw new Error('No SIP installments could be calculated');
    }

    // Get current price to calculate current value
    try {
      const currentPriceData = await this.stockDataService.getCurrentPrice(
        stockSymbol.split('-')[0], // Extract symbol from token
        stockSymbol,
        'NSE'
      );

      const currentValue = totalUnits * currentPriceData.currentPrice;
      const totalReturn = currentValue - totalInvested;
      const years = calculateYearsBetweenDates(startDate, endDate);
      const cagr = calculateCAGR(totalInvested, currentValue, years);
      const absoluteReturn = calculateAbsoluteReturn(totalInvested, currentValue);

      return {
        totalInvested,
        currentValue,
        totalReturn,
        cagr,
        absoluteReturn,
        installments,
      };
    } catch (error) {
      throw new Error(`Failed to calculate SIP returns: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate optimal investment timing (dollar-cost averaging analysis)
   */
  async calculateOptimalTiming(
    stockSymbol: string,
    totalAmount: number,
    startDate: Date,
    endDate: Date,
    strategies: Array<{
      name: string;
      type: 'lump_sum' | 'monthly_sip' | 'quarterly_sip';
    }> = [
      { name: 'Lump Sum at Start', type: 'lump_sum' },
      { name: 'Monthly SIP', type: 'monthly_sip' },
      { name: 'Quarterly SIP', type: 'quarterly_sip' },
    ]
  ): Promise<Array<{
    strategy: string;
    totalInvested: number;
    currentValue: number;
    cagr: number;
    absoluteReturn: number;
  }>> {
    const results: Array<{
      strategy: string;
      totalInvested: number;
      currentValue: number;
      cagr: number;
      absoluteReturn: number;
    }> = [];

    for (const strategy of strategies) {
      try {
        let result;

        switch (strategy.type) {
          case 'lump_sum':
            const scenario = this.createScenario(stockSymbol, totalAmount, startDate, endDate);
            const lumpSumResult = await this.calculateInvestmentResult(scenario);
            result = {
              strategy: strategy.name,
              totalInvested: lumpSumResult.initialValue,
              currentValue: lumpSumResult.currentValue,
              cagr: lumpSumResult.cagr,
              absoluteReturn: lumpSumResult.absoluteReturn,
            };
            break;

          case 'monthly_sip':
            const months = Math.ceil(calculateYearsBetweenDates(startDate, endDate) * 12);
            const monthlyAmount = totalAmount / months;
            const monthlySipResult = await this.calculateSIPReturns(
              stockSymbol,
              monthlyAmount,
              startDate,
              endDate
            );
            result = {
              strategy: strategy.name,
              totalInvested: monthlySipResult.totalInvested,
              currentValue: monthlySipResult.currentValue,
              cagr: monthlySipResult.cagr,
              absoluteReturn: monthlySipResult.absoluteReturn,
            };
            break;

          case 'quarterly_sip':
            const quarters = Math.ceil(calculateYearsBetweenDates(startDate, endDate) * 4);
            const quarterlyAmount = totalAmount / quarters;
            // For quarterly, we'll simulate with 3-month intervals
            const quarterlySipResult = await this.calculateSIPReturns(
              stockSymbol,
              quarterlyAmount,
              startDate,
              endDate
            );
            result = {
              strategy: strategy.name,
              totalInvested: quarterlySipResult.totalInvested,
              currentValue: quarterlySipResult.currentValue,
              cagr: quarterlySipResult.cagr,
              absoluteReturn: quarterlySipResult.absoluteReturn,
            };
            break;

          default:
            continue;
        }

        results.push(result);
      } catch (error) {
        console.error(`Error calculating ${strategy.name}:`, error);
        // Continue with other strategies
      }
    }

    return results;
  }
}
