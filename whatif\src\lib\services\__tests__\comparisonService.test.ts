import { ComparisonService } from '../comparisonService';
import { InvestmentCalculator } from '../investmentCalculator';
import { BenchmarkDataService } from '../benchmarkData';
import { InvestmentScenario, InvestmentResult, ComparisonResult } from '../../types';

// Mock the services
jest.mock('../investmentCalculator');
jest.mock('../benchmarkData');

describe('ComparisonService', () => {
  let comparisonService: ComparisonService;
  let mockInvestmentCalculator: jest.Mocked<InvestmentCalculator>;
  let mockBenchmarkDataService: jest.Mocked<BenchmarkDataService>;

  beforeEach(() => {
    mockInvestmentCalculator = new InvestmentCalculator({} as any, {} as any) as jest.Mocked<InvestmentCalculator>;
    mockBenchmarkDataService = new BenchmarkDataService({} as any) as jest.Mocked<BenchmarkDataService>;
    
    comparisonService = new ComparisonService(
      mockInvestmentCalculator,
      mockBenchmarkDataService
    );
  });

  describe('generateComparisonSummary', () => {
    const mockScenario: InvestmentScenario = {
      id: 'test-scenario',
      stockSymbol: 'SBIN-EQ',
      investmentAmount: 100000,
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      createdAt: new Date(),
    };

    const mockComparisonResult: ComparisonResult = {
      investment: {
        scenario: mockScenario,
        initialValue: 100000,
        currentValue: 120000,
        absoluteReturn: 20,
        cagr: 20,
        totalReturn: 20000,
        annualizedReturn: 20,
      },
      benchmarks: {
        GOLD: {
          initialValue: 100000,
          currentValue: 110000,
          cagr: 10,
          absoluteReturn: 10,
        },
        FD: {
          initialValue: 100000,
          currentValue: 106500,
          cagr: 6.5,
          absoluteReturn: 6.5,
        },
        NIFTY: {
          initialValue: 100000,
          currentValue: 115000,
          cagr: 15,
          absoluteReturn: 15,
        },
      },
    };

    it('should generate comprehensive comparison summary', async () => {
      mockInvestmentCalculator.calculateWithComparisons.mockResolvedValue(mockComparisonResult);

      const summary = await comparisonService.generateComparisonSummary(mockScenario);

      expect(summary.investment.name).toBe('SBIN-EQ Investment');
      expect(summary.investment.cagr).toBe(20);
      expect(summary.investment.rank).toBe(1); // Best performer

      expect(summary.benchmarks).toHaveLength(3);
      expect(summary.benchmarks[0].name).toBe('Nifty 50'); // Second best
      expect(summary.benchmarks[0].rank).toBe(2);

      expect(summary.insights).toContain(
        expect.stringContaining('outperformed all benchmarks')
      );
    });

    it('should handle calculation errors', async () => {
      mockInvestmentCalculator.calculateWithComparisons.mockRejectedValue(new Error('Calculation failed'));

      await expect(
        comparisonService.generateComparisonSummary(mockScenario)
      ).rejects.toThrow('Calculation failed');
    });
  });

  describe('calculatePerformanceMetrics', () => {
    const mockSummary = {
      investment: {
        name: 'SBIN-EQ Investment',
        initialValue: 100000,
        currentValue: 120000,
        totalReturn: 20000,
        cagr: 20,
        absoluteReturn: 20,
        rank: 1,
      },
      benchmarks: [
        {
          name: 'Nifty 50',
          type: 'NIFTY',
          initialValue: 100000,
          currentValue: 115000,
          totalReturn: 15000,
          cagr: 15,
          absoluteReturn: 15,
          rank: 2,
          outperformance: 5,
        },
        {
          name: 'Gold',
          type: 'GOLD',
          initialValue: 100000,
          currentValue: 110000,
          totalReturn: 10000,
          cagr: 10,
          absoluteReturn: 10,
          rank: 3,
          outperformance: 10,
        },
        {
          name: 'Fixed Deposit',
          type: 'FD',
          initialValue: 100000,
          currentValue: 106500,
          totalReturn: 6500,
          cagr: 6.5,
          absoluteReturn: 6.5,
          rank: 4,
          outperformance: 13.5,
        },
      ],
      insights: [],
    };

    it('should calculate performance metrics correctly', () => {
      const metrics = comparisonService.calculatePerformanceMetrics(mockSummary);

      expect(metrics.bestPerformer.name).toBe('SBIN-EQ Investment');
      expect(metrics.bestPerformer.cagr).toBe(20);

      expect(metrics.worstPerformer.name).toBe('Fixed Deposit');
      expect(metrics.worstPerformer.cagr).toBe(6.5);

      expect(metrics.averageCagr).toBe(12.88); // (20+15+10+6.5)/4 = 12.875

      expect(metrics.volatilityRanking).toHaveLength(4);
      expect(metrics.volatilityRanking[0].name).toBe('SBIN-EQ Investment'); // Lowest volatility (0)
    });
  });

  describe('generateComparisonChartData', () => {
    const mockScenario: InvestmentScenario = {
      id: 'test-scenario',
      stockSymbol: 'SBIN-EQ',
      investmentAmount: 100000,
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      createdAt: new Date(),
    };

    const mockComparisonResult: ComparisonResult = {
      investment: {
        scenario: mockScenario,
        initialValue: 100000,
        currentValue: 120000,
        absoluteReturn: 20,
        cagr: 20,
        totalReturn: 20000,
        annualizedReturn: 20,
      },
      benchmarks: {
        GOLD: {
          initialValue: 100000,
          currentValue: 110000,
          cagr: 10,
          absoluteReturn: 10,
        },
        FD: {
          initialValue: 100000,
          currentValue: 106500,
          cagr: 6.5,
          absoluteReturn: 6.5,
        },
        NIFTY: {
          initialValue: 100000,
          currentValue: 115000,
          cagr: 15,
          absoluteReturn: 15,
        },
      },
    };

    it('should generate chart data for visualization', async () => {
      mockInvestmentCalculator.calculateWithComparisons.mockResolvedValue(mockComparisonResult);

      const chartData = await comparisonService.generateComparisonChartData(mockScenario);

      expect(chartData.timeSeriesData).toBeDefined();
      expect(chartData.timeSeriesData.length).toBeGreaterThan(0);
      expect(chartData.timeSeriesData[0]).toHaveProperty('date');
      expect(chartData.timeSeriesData[0]).toHaveProperty('investment');
      expect(chartData.timeSeriesData[0]).toHaveProperty('gold');
      expect(chartData.timeSeriesData[0]).toHaveProperty('fd');
      expect(chartData.timeSeriesData[0]).toHaveProperty('nifty');

      expect(chartData.barChartData).toHaveLength(4);
      expect(chartData.barChartData[0].name).toBe('SBIN-EQ');
      expect(chartData.barChartData[0].cagr).toBe(20);
    });
  });

  describe('compareMultipleStocks', () => {
    const mockScenarios: InvestmentScenario[] = [
      {
        id: 'scenario-1',
        stockSymbol: 'SBIN-EQ',
        investmentAmount: 100000,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-12-31'),
        createdAt: new Date(),
      },
      {
        id: 'scenario-2',
        stockSymbol: 'RELIANCE-EQ',
        investmentAmount: 100000,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-12-31'),
        createdAt: new Date(),
      },
    ];

    it('should compare multiple stocks successfully', async () => {
      const mockSummary = {
        investment: {
          name: 'Test Investment',
          initialValue: 100000,
          currentValue: 120000,
          totalReturn: 20000,
          cagr: 20,
          absoluteReturn: 20,
          rank: 1,
        },
        benchmarks: [],
        insights: [],
      };

      // Mock the generateComparisonSummary method
      jest.spyOn(comparisonService, 'generateComparisonSummary').mockResolvedValue(mockSummary);

      const results = await comparisonService.compareMultipleStocks(mockScenarios);

      expect(results).toHaveLength(2);
      expect(results[0].scenario.stockSymbol).toBe('SBIN-EQ');
      expect(results[1].scenario.stockSymbol).toBe('RELIANCE-EQ');
      expect(results[0].summary).toEqual(mockSummary);
    });

    it('should continue with other stocks if one fails', async () => {
      jest.spyOn(comparisonService, 'generateComparisonSummary')
        .mockRejectedValueOnce(new Error('Failed for first stock'))
        .mockResolvedValueOnce({
          investment: {
            name: 'Test Investment',
            initialValue: 100000,
            currentValue: 120000,
            totalReturn: 20000,
            cagr: 20,
            absoluteReturn: 20,
            rank: 1,
          },
          benchmarks: [],
          insights: [],
        });

      const results = await comparisonService.compareMultipleStocks(mockScenarios);

      expect(results).toHaveLength(1);
      expect(results[0].scenario.stockSymbol).toBe('RELIANCE-EQ');
    });
  });
});
