// Integration test for Angel One API with our actual implementation
const axios = require('axios');
const { authenticator } = require('otplib');

const API_KEY = 'TU9sOEpR';
const CLIENT_ID = 'M834963';
const PASSWORD = '3318';
const TOTP_SECRET = 'CRAFUYSVQVWTWPHVWZ55KV5VJI';

class SimpleAngelOneClient {
  constructor() {
    this.jwtToken = null;
    this.baseURL = 'https://apiconnect.angelone.in';
  }

  async login() {
    try {
      const totp = authenticator.generate(TOTP_SECRET);
      console.log('📱 Generated TOTP:', totp);

      const response = await axios.post(`${this.baseURL}/rest/auth/angelbroking/user/v1/loginByPassword`, {
        clientcode: CLIENT_ID,
        password: PASSWORD,
        totp: totp
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': API_KEY,
        }
      });

      if (response.data.status && response.data.data) {
        this.jwtToken = response.data.data.jwtToken;
        return { success: true, message: 'Login successful' };
      } else {
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  async getHistoricalData(symbolToken, exchange, startDate, endDate) {
    if (!this.jwtToken) {
      throw new Error('Not authenticated');
    }

    const fromdate = startDate.toISOString().slice(0, 16).replace('T', ' ');
    const todate = endDate.toISOString().slice(0, 16).replace('T', ' ');

    const response = await axios.post(`${this.baseURL}/rest/secure/angelbroking/historical/v1/getCandleData`, {
      exchange: exchange,
      symboltoken: symbolToken,
      interval: 'ONE_DAY',
      fromdate: fromdate,
      todate: todate
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-UserType': 'USER',
        'X-SourceID': 'WEB',
        'X-ClientLocalIP': '127.0.0.1',
        'X-ClientPublicIP': '127.0.0.1',
        'X-MACAddress': '00:00:00:00:00:00',
        'X-PrivateKey': API_KEY,
        'Authorization': `Bearer ${this.jwtToken}`
      }
    });

    if (response.data.status && response.data.data) {
      return response.data.data.map(item => ({
        date: new Date(item[0]),
        open: item[1],
        high: item[2],
        low: item[3],
        close: item[4],
        volume: item[5]
      }));
    } else {
      throw new Error(response.data.message || 'Failed to fetch historical data');
    }
  }

  async getCurrentPrice(tradingSymbol, symbolToken, exchange) {
    if (!this.jwtToken) {
      throw new Error('Not authenticated');
    }

    const response = await axios.post(`${this.baseURL}/rest/secure/angelbroking/order/v1/getLtpData`, {
      exchange: exchange,
      tradingsymbol: tradingSymbol,
      symboltoken: symbolToken
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-UserType': 'USER',
        'X-SourceID': 'WEB',
        'X-ClientLocalIP': '127.0.0.1',
        'X-ClientPublicIP': '127.0.0.1',
        'X-MACAddress': '00:00:00:00:00:00',
        'X-PrivateKey': API_KEY,
        'Authorization': `Bearer ${this.jwtToken}`
      }
    });

    if (response.data.status && response.data.data) {
      const data = response.data.data;
      return {
        symbol: data.tradingsymbol,
        name: data.tradingsymbol,
        exchange: data.exchange,
        token: data.symboltoken,
        currentPrice: data.ltp,
        lastUpdated: new Date()
      };
    } else {
      throw new Error(response.data.message || 'Failed to fetch current price');
    }
  }

  // Calculate investment returns
  calculateInvestmentResult(investmentAmount, startPrice, endPrice, startDate, endDate) {
    const numberOfShares = investmentAmount / startPrice;
    const currentValue = numberOfShares * endPrice;
    const totalReturn = currentValue - investmentAmount;
    const absoluteReturn = (totalReturn / investmentAmount) * 100;
    
    // Calculate years
    const timeDiff = endDate.getTime() - startDate.getTime();
    const years = timeDiff / (1000 * 3600 * 24 * 365.25);
    
    // Calculate CAGR
    const cagr = years > 0 ? (Math.pow(currentValue / investmentAmount, 1 / years) - 1) * 100 : 0;

    return {
      investmentAmount,
      numberOfShares: Math.round(numberOfShares * 100) / 100,
      startPrice,
      endPrice,
      currentValue: Math.round(currentValue * 100) / 100,
      totalReturn: Math.round(totalReturn * 100) / 100,
      absoluteReturn: Math.round(absoluteReturn * 100) / 100,
      cagr: Math.round(cagr * 100) / 100,
      years: Math.round(years * 100) / 100
    };
  }
}

async function testIntegration() {
  console.log('🚀 Testing Angel One API Integration...\n');
  
  const client = new SimpleAngelOneClient();
  
  try {
    // Test login
    console.log('🔐 Step 1: Testing login...');
    const loginResult = await client.login();
    console.log('Login result:', loginResult);
    
    if (!loginResult.success) {
      console.log('❌ Login failed, stopping test');
      return;
    }
    
    console.log('✅ Login successful!\n');

    // Test historical data
    console.log('📈 Step 2: Testing historical data fetch...');
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-01-31');
    
    const historicalData = await client.getHistoricalData('3045', 'NSE', startDate, endDate);
    console.log(`✅ Fetched ${historicalData.length} historical data points`);
    console.log('First data point:', historicalData[0]);
    console.log('Last data point:', historicalData[historicalData.length - 1]);
    console.log('');

    // Test current price
    console.log('💰 Step 3: Testing current price fetch...');
    const currentPriceData = await client.getCurrentPrice('SBIN-EQ', '3045', 'NSE');
    console.log('✅ Current price data:', currentPriceData);
    console.log('');

    // Test investment calculation
    console.log('📊 Step 4: Testing investment calculation...');
    const investmentAmount = 100000; // ₹1,00,000
    const startPrice = historicalData[0].close;
    const endPrice = historicalData[historicalData.length - 1].close;
    
    const investmentResult = client.calculateInvestmentResult(
      investmentAmount,
      startPrice,
      endPrice,
      startDate,
      endDate
    );
    
    console.log('✅ Investment calculation result:');
    console.log(`   💵 Investment Amount: ₹${investmentResult.investmentAmount.toLocaleString()}`);
    console.log(`   📈 Start Price: ₹${investmentResult.startPrice}`);
    console.log(`   📈 End Price: ₹${investmentResult.endPrice}`);
    console.log(`   🔢 Shares Bought: ${investmentResult.numberOfShares}`);
    console.log(`   💰 Current Value: ₹${investmentResult.currentValue.toLocaleString()}`);
    console.log(`   📊 Total Return: ₹${investmentResult.totalReturn.toLocaleString()}`);
    console.log(`   📈 Absolute Return: ${investmentResult.absoluteReturn}%`);
    console.log(`   🎯 CAGR: ${investmentResult.cagr}%`);
    console.log(`   ⏰ Investment Period: ${investmentResult.years} years`);
    
    console.log('\n🎉 All tests passed! Angel One API integration is working perfectly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testIntegration();
