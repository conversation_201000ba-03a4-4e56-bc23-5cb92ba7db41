import { StockDataService } from '../stockData';
import { AngelOneClient } from '../../api/angelone';
import { HistoricalPrice, StockData, InvestmentScenario } from '../../types';

// Mock the AngelOneClient
jest.mock('../../api/angelone');

describe('StockDataService', () => {
  let stockDataService: StockDataService;
  let mockAngelOneClient: jest.Mocked<AngelOneClient>;

  beforeEach(() => {
    mockAngelOneClient = new AngelOneClient({
      apiKey: 'test-key',
      clientId: 'test-client',
      password: 'test-password',
      totpSecret: 'test-secret',
    }) as jest.Mocked<AngelOneClient>;

    stockDataService = new StockDataService(mockAngelOneClient);
  });

  describe('getHistoricalPrices', () => {
    const mockHistoricalData: HistoricalPrice[] = [
      {
        date: new Date('2023-01-01'),
        open: 100,
        high: 105,
        low: 95,
        close: 102,
        volume: 1000,
      },
      {
        date: new Date('2023-01-02'),
        open: 102,
        high: 108,
        low: 100,
        close: 106,
        volume: 1200,
      },
    ];

    it('should fetch historical prices successfully', async () => {
      mockAngelOneClient.getHistoricalData.mockResolvedValue(mockHistoricalData);

      const result = await stockDataService.getHistoricalPrices(
        '3045',
        'NSE',
        new Date('2023-01-01'),
        new Date('2023-01-02')
      );

      expect(result).toEqual(mockHistoricalData);
      expect(mockAngelOneClient.getHistoricalData).toHaveBeenCalledWith(
        expect.objectContaining({
          exchange: 'NSE',
          symboltoken: '3045',
          interval: 'ONE_DAY',
        })
      );
    });

    it('should validate date range', async () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);

      await expect(
        stockDataService.getHistoricalPrices(
          '3045',
          'NSE',
          new Date(),
          futureDate
        )
      ).rejects.toThrow('End date cannot be in the future');
    });

    it('should handle API errors', async () => {
      mockAngelOneClient.getHistoricalData.mockRejectedValue(new Error('API Error'));

      await expect(
        stockDataService.getHistoricalPrices(
          '3045',
          'NSE',
          new Date('2023-01-01'),
          new Date('2023-01-02')
        )
      ).rejects.toThrow('Failed to fetch historical data: API Error');
    });
  });

  describe('getCurrentPrice', () => {
    const mockStockData: StockData = {
      symbol: 'SBIN-EQ',
      name: 'SBIN-EQ',
      exchange: 'NSE',
      token: '3045',
      currentPrice: 500,
      lastUpdated: new Date(),
    };

    it('should fetch current price successfully', async () => {
      mockAngelOneClient.getCurrentPrice.mockResolvedValue(mockStockData);

      const result = await stockDataService.getCurrentPrice('SBIN-EQ', '3045', 'NSE');

      expect(result).toEqual(mockStockData);
      expect(mockAngelOneClient.getCurrentPrice).toHaveBeenCalledWith({
        exchange: 'NSE',
        tradingsymbol: 'SBIN-EQ',
        symboltoken: '3045',
      });
    });

    it('should handle API errors', async () => {
      mockAngelOneClient.getCurrentPrice.mockRejectedValue(new Error('API Error'));

      await expect(
        stockDataService.getCurrentPrice('SBIN-EQ', '3045', 'NSE')
      ).rejects.toThrow('Failed to fetch current price: API Error');
    });
  });

  describe('calculateInvestmentResult', () => {
    const mockScenario: InvestmentScenario = {
      id: 'test-scenario',
      stockSymbol: '3045',
      investmentAmount: 10000,
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      createdAt: new Date(),
    };

    const mockHistoricalData: HistoricalPrice[] = [
      {
        date: new Date('2023-01-01'),
        open: 100,
        high: 105,
        low: 95,
        close: 100, // Start price
        volume: 1000,
      },
      {
        date: new Date('2023-12-31'),
        open: 120,
        high: 125,
        low: 115,
        close: 120, // End price
        volume: 1200,
      },
    ];

    it('should calculate investment result correctly', async () => {
      mockAngelOneClient.getHistoricalData.mockResolvedValue(mockHistoricalData);

      const result = await stockDataService.calculateInvestmentResult(mockScenario);

      expect(result.scenario).toEqual(mockScenario);
      expect(result.initialValue).toBe(10000);
      expect(result.currentValue).toBe(12000); // (10000/100) * 120
      expect(result.absoluteReturn).toBe(20); // ((12000-10000)/10000) * 100
      expect(result.totalReturn).toBe(2000);
      expect(result.cagr).toBeCloseTo(20, 0); // Approximately 20% for 1 year
    });

    it('should handle no historical data', async () => {
      mockAngelOneClient.getHistoricalData.mockResolvedValue([]);

      await expect(
        stockDataService.calculateInvestmentResult(mockScenario)
      ).rejects.toThrow('No historical data available for the specified period');
    });
  });

  describe('getPriceAtDate', () => {
    const mockHistoricalData: HistoricalPrice[] = [
      {
        date: new Date('2023-01-01'),
        open: 100,
        high: 105,
        low: 95,
        close: 102,
        volume: 1000,
      },
      {
        date: new Date('2023-01-03'),
        open: 102,
        high: 108,
        low: 100,
        close: 106,
        volume: 1200,
      },
    ];

    it('should find closest price to target date', async () => {
      mockAngelOneClient.getHistoricalData.mockResolvedValue(mockHistoricalData);

      const result = await stockDataService.getPriceAtDate(
        '3045',
        'NSE',
        new Date('2023-01-02') // Between the two dates
      );

      expect(result).not.toBeNull();
      expect(result?.price).toBe(102); // Closer to Jan 1st
      expect(result?.actualDate).toEqual(new Date('2023-01-01'));
    });

    it('should return null when no data available', async () => {
      mockAngelOneClient.getHistoricalData.mockResolvedValue([]);

      const result = await stockDataService.getPriceAtDate(
        '3045',
        'NSE',
        new Date('2023-01-01')
      );

      expect(result).toBeNull();
    });
  });

  describe('validateStock', () => {
    const mockStockData: StockData = {
      symbol: 'SBIN-EQ',
      name: 'SBIN-EQ',
      exchange: 'NSE',
      token: '3045',
      currentPrice: 500,
      lastUpdated: new Date(),
    };

    it('should validate valid stock', async () => {
      mockAngelOneClient.getCurrentPrice.mockResolvedValue(mockStockData);

      const result = await stockDataService.validateStock('SBIN-EQ', '3045', 'NSE');

      expect(result.isValid).toBe(true);
      expect(result.stockData).toEqual(mockStockData);
      expect(result.error).toBeUndefined();
    });

    it('should handle invalid stock', async () => {
      mockAngelOneClient.getCurrentPrice.mockRejectedValue(new Error('Stock not found'));

      const result = await stockDataService.validateStock('INVALID', '0000', 'NSE');

      expect(result.isValid).toBe(false);
      expect(result.stockData).toBeUndefined();
      expect(result.error).toBe('Failed to fetch current price: Stock not found');
    });
  });
});
