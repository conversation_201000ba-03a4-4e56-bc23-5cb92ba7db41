// Test our service classes with real Angel One API
const { AngelOneClient } = require('./src/lib/api/angelone');
const { StockDataService } = require('./src/lib/services/stockData');

async function testOurServices() {
  console.log('🔄 Testing our service classes with real API...');
  
  try {
    // Initialize client
    const client = new AngelOneClient({
      apiKey: 'TU9sOEpR',
      clientId: 'M834963',
      password: '3318',
      totpSecret: 'CRAFUYSVQVWTWPHVWZ55KV5VJI',
    });

    // Initialize service
    const stockService = new StockDataService(client);

    // Test login
    console.log('🔐 Testing login through our client...');
    const loginResult = await client.login();
    console.log('Login result:', loginResult);

    if (loginResult.success) {
      console.log('✅ Login successful!');

      // Test our historical data service
      console.log('\n📈 Testing our historical data service...');
      try {
        const historicalData = await stockService.getHistoricalPrices(
          '3045', // SBIN token
          'NSE',
          new Date('2024-01-01'),
          new Date('2024-01-31')
        );
        
        console.log('Historical data points received:', historicalData.length);
        if (historicalData.length > 0) {
          console.log('First data point:', historicalData[0]);
          console.log('Last data point:', historicalData[historicalData.length - 1]);
        }
      } catch (error) {
        console.error('❌ Historical data service error:', error.message);
      }

      // Test our current price service
      console.log('\n💰 Testing our current price service...');
      try {
        const stockData = await stockService.getCurrentPrice('SBIN-EQ', '3045', 'NSE');
        console.log('Stock data:', stockData);
      } catch (error) {
        console.error('❌ Current price service error:', error.message);
      }

      // Test investment calculation
      console.log('\n📊 Testing investment calculation...');
      try {
        const scenario = {
          id: 'test-scenario',
          stockSymbol: '3045',
          investmentAmount: 100000,
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-01-31'),
          createdAt: new Date(),
        };

        const result = await stockService.calculateInvestmentResult(scenario);
        console.log('Investment result:', result);
      } catch (error) {
        console.error('❌ Investment calculation error:', error.message);
      }

      // Logout
      console.log('\n🔓 Logging out...');
      await client.logout();
      console.log('✅ Logged out successfully');

    } else {
      console.log('❌ Login failed:', loginResult.message);
    }

  } catch (error) {
    console.error('❌ Service test failed:', error.message);
  }
}

testOurServices();
