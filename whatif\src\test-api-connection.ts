// Test Angel One API connection with real credentials
import { AngelOneClient } from './lib/api/angelone';

async function testAPIConnection() {
  console.log('🔄 Testing Angel One API connection...');
  
  const client = new AngelOneClient({
    apiKey: 'TU9sOEpR',
    clientId: 'M834963',
    password: '3318',
    totpSecret: 'CRAFUYSVQVWTWPHVWZ55KV5VJI',
  });

  try {
    // Test login
    console.log('🔐 Testing login...');
    const loginResult = await client.login();
    console.log('Login result:', loginResult);

    if (loginResult.success) {
      console.log('✅ Login successful!');
      
      // Test historical data
      console.log('\n📈 Testing historical data fetch...');
      try {
        const historicalData = await client.getHistoricalData({
          exchange: 'NSE',
          symboltoken: '3045', // SBIN token
          interval: 'ONE_DAY',
          fromdate: '2024-01-01 09:15',
          todate: '2024-01-31 15:30',
        });
        
        console.log('Historical data points received:', historicalData.length);
        if (historicalData.length > 0) {
          console.log('Sample data point:', historicalData[0]);
        }
      } catch (error) {
        console.error('❌ Historical data error:', error);
      }

      // Test current price
      console.log('\n💰 Testing current price fetch...');
      try {
        const currentPrice = await client.getCurrentPrice({
          exchange: 'NSE',
          tradingsymbol: 'SBIN-EQ',
          symboltoken: '3045',
        });
        
        console.log('Current price data:', currentPrice);
      } catch (error) {
        console.error('❌ Current price error:', error);
      }

      // Logout
      console.log('\n🔓 Testing logout...');
      const logoutResult = await client.logout();
      console.log('Logout result:', logoutResult);

    } else {
      console.log('❌ Login failed:', loginResult.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testAPIConnection().catch(console.error);
