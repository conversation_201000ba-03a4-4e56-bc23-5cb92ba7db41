// Angel One API client for the What If investment analysis tool

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { authenticator } from 'otplib';
import { API_CONFIG } from '../config';
import { RateLimiter, sanitizeForLogging } from '../security';
import {
  AngelOneLoginRequest,
  AngelOneLoginResponse,
  AngelOneHistoricalRequest,
  AngelOneHistoricalResponse,
  AngelOneLTPRequest,
  AngelOneLTPResponse,
  HistoricalPrice,
  StockData,
} from '../types';

export class AngelOneClient {
  private axiosInstance: AxiosInstance;
  private jwtToken: string | null = null;
  private refreshToken: string | null = null;
  private feedToken: string | null = null;
  private rateLimiter: RateLimiter;
  private readonly apiKey: string;
  private readonly clientId: string;
  private readonly password: string;
  private readonly totpSecret: string;

  constructor(config: {
    apiKey: string;
    clientId: string;
    password: string;
    totpSecret: string;
  }) {
    this.apiKey = config.apiKey;
    this.clientId = config.clientId;
    this.password = config.password;
    this.totpSecret = config.totpSecret;

    // Initialize rate limiter
    this.rateLimiter = new RateLimiter(
      API_CONFIG.rateLimit.requestsPerSecond,
      1
    );

    // Create axios instance
    this.axiosInstance = axios.create({
      baseURL: API_CONFIG.angelOne.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-UserType': 'USER',
        'X-SourceID': 'WEB',
        'X-ClientLocalIP': '127.0.0.1',
        'X-ClientPublicIP': '127.0.0.1',
        'X-MACAddress': '00:00:00:00:00:00',
        'X-PrivateKey': this.apiKey,
      },
    });

    // Add request interceptor for rate limiting
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        // Wait if rate limit exceeded
        while (!this.rateLimiter.isAllowed()) {
          const waitTime = this.rateLimiter.getTimeUntilReset();
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        // Add JWT token if available
        if (this.jwtToken) {
          config.headers.Authorization = `Bearer ${this.jwtToken}`;
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && this.refreshToken) {
          // Try to refresh token
          try {
            await this.refreshAuthToken();
            // Retry the original request
            return this.axiosInstance.request(error.config);
          } catch (refreshError) {
            // Refresh failed, need to re-login
            this.clearTokens();
            throw new Error('Authentication failed. Please login again.');
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Generate TOTP for authentication
   */
  private generateTOTP(): string {
    return authenticator.generate(this.totpSecret);
  }

  /**
   * Clear stored tokens
   */
  private clearTokens(): void {
    this.jwtToken = null;
    this.refreshToken = null;
    this.feedToken = null;
  }

  /**
   * Login to Angel One API
   */
  async login(): Promise<{ success: boolean; message: string }> {
    try {
      const totp = this.generateTOTP();
      
      const loginRequest: AngelOneLoginRequest = {
        clientcode: this.clientId,
        password: this.password,
        totp: totp,
      };

      console.log('Attempting login with:', sanitizeForLogging(loginRequest as unknown as Record<string, unknown>));

      const response: AxiosResponse<AngelOneLoginResponse> = await this.axiosInstance.post(
        API_CONFIG.angelOne.endpoints.login,
        loginRequest
      );

      if (response.data.status && response.data.data) {
        this.jwtToken = response.data.data.jwtToken;
        this.refreshToken = response.data.data.refreshToken;
        this.feedToken = response.data.data.feedToken;

        return {
          success: true,
          message: 'Login successful',
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Login failed',
        };
      }
    } catch (error) {
      console.error('Login error:', sanitizeForLogging({ error } as Record<string, unknown>));
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Login failed',
      };
    }
  }

  /**
   * Refresh authentication token
   */
  private async refreshAuthToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.axiosInstance.post('/rest/auth/angelbroking/jwt/v1/generateTokens', {
      refreshToken: this.refreshToken,
    });

    if (response.data.status && response.data.data) {
      this.jwtToken = response.data.data.jwtToken;
      this.refreshToken = response.data.data.refreshToken;
    } else {
      throw new Error('Token refresh failed');
    }
  }

  /**
   * Check if client is authenticated
   */
  isAuthenticated(): boolean {
    return this.jwtToken !== null;
  }

  /**
   * Get historical data for a stock
   */
  async getHistoricalData(request: AngelOneHistoricalRequest): Promise<HistoricalPrice[]> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated. Please login first.');
    }

    try {
      const response: AxiosResponse<AngelOneHistoricalResponse> = await this.axiosInstance.post(
        API_CONFIG.angelOne.endpoints.historicalData,
        request
      );

      if (response.data.status && response.data.data) {
        return response.data.data.map(item => ({
          date: new Date(item[0]),
          open: item[1],
          high: item[2],
          low: item[3],
          close: item[4],
          volume: item[5],
        }));
      } else {
        throw new Error(response.data.message || 'Failed to fetch historical data');
      }
    } catch (error) {
      console.error('Historical data error:', sanitizeForLogging({ error, request } as Record<string, unknown>));
      throw error;
    }
  }

  /**
   * Get current price (LTP) for a stock
   */
  async getCurrentPrice(request: AngelOneLTPRequest): Promise<StockData> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated. Please login first.');
    }

    try {
      const response: AxiosResponse<AngelOneLTPResponse> = await this.axiosInstance.post(
        API_CONFIG.angelOne.endpoints.ltp,
        request
      );

      if (response.data.status && response.data.data) {
        const data = response.data.data;
        return {
          symbol: data.tradingsymbol,
          name: data.tradingsymbol, // Angel One doesn't provide company name in LTP response
          exchange: data.exchange as 'NSE' | 'BSE',
          token: data.symboltoken,
          currentPrice: data.ltp,
          lastUpdated: new Date(),
        };
      } else {
        throw new Error(response.data.message || 'Failed to fetch current price');
      }
    } catch (error) {
      console.error('Current price error:', sanitizeForLogging({ error, request } as Record<string, unknown>));
      throw error;
    }
  }

  /**
   * Logout from Angel One API
   */
  async logout(): Promise<{ success: boolean; message: string }> {
    if (!this.isAuthenticated()) {
      return { success: true, message: 'Already logged out' };
    }

    try {
      await this.axiosInstance.post(API_CONFIG.angelOne.endpoints.logout, {
        clientcode: this.clientId,
      });

      this.clearTokens();
      
      return {
        success: true,
        message: 'Logout successful',
      };
    } catch (error) {
      console.error('Logout error:', sanitizeForLogging({ error } as Record<string, unknown>));
      this.clearTokens(); // Clear tokens anyway
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Logout failed',
      };
    }
  }
}
